<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Create new user
     */
    public function create_user($data) {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('users', $data);
    }

    /**
     * Get user by email
     */
    public function get_by_email($email) {
        return $this->db->where('email', $email)->get('users')->row();
    }

    /**
     * Get user by username
     */
    public function get_by_username($username) {
        return $this->db->where('username', $username)->get('users')->row();
    }

    /**
     * Get user by ID
     */
    public function get_by_id($id) {
        return $this->db->where('id', $id)->get('users')->row();
    }

    /**
     * Verify user login
     */
    public function verify_login($email_or_username, $password) {
        $this->db->group_start();
        $this->db->where('email', $email_or_username);
        $this->db->or_where('username', $email_or_username);
        $this->db->group_end();
        $this->db->where('status', 'active');
        
        $user = $this->db->get('users')->row();
        
        if ($user && password_verify($password, $user->password)) {
            return $user;
        }
        
        return false;
    }

    /**
     * Update user
     */
    public function update_user($id, $data) {
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->where('id', $id)->update('users', $data);
    }

    /**
     * Check if email exists
     */
    public function email_exists($email, $exclude_id = null) {
        $this->db->where('email', $email);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get('users')->num_rows() > 0;
    }

    /**
     * Check if username exists
     */
    public function username_exists($username, $exclude_id = null) {
        $this->db->where('username', $username);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get('users')->num_rows() > 0;
    }

    /**
     * Create password reset token
     */
    public function create_reset_token($email) {
        $token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $data = [
            'email' => $email,
            'token' => $token,
            'expires_at' => $expires_at,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('password_resets', $data);
        return $token;
    }

    /**
     * Verify reset token
     */
    public function verify_reset_token($token) {
        $this->db->where('token', $token);
        $this->db->where('expires_at >', date('Y-m-d H:i:s'));
        $this->db->where('used', 0);
        
        return $this->db->get('password_resets')->row();
    }

    /**
     * Use reset token
     */
    public function use_reset_token($token) {
        return $this->db->where('token', $token)->update('password_resets', ['used' => 1]);
    }

    /**
     * Create user session
     */
    public function create_session($user_id, $ip_address, $user_agent) {
        $token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        $data = [
            'user_id' => $user_id,
            'session_token' => $token,
            'ip_address' => $ip_address,
            'user_agent' => $user_agent,
            'expires_at' => $expires_at,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('user_sessions', $data);
        return $token;
    }

    /**
     * Verify session token
     */
    public function verify_session($token) {
        $this->db->select('us.*, u.*');
        $this->db->from('user_sessions us');
        $this->db->join('users u', 'u.id = us.user_id');
        $this->db->where('us.session_token', $token);
        $this->db->where('us.expires_at >', date('Y-m-d H:i:s'));
        $this->db->where('u.status', 'active');
        
        return $this->db->get()->row();
    }

    /**
     * Delete session
     */
    public function delete_session($token) {
        return $this->db->where('session_token', $token)->delete('user_sessions');
    }

    /**
     * Delete expired sessions
     */
    public function cleanup_expired_sessions() {
        return $this->db->where('expires_at <', date('Y-m-d H:i:s'))->delete('user_sessions');
    }

    /**
     * Get all users with pagination
     */
    public function get_users($limit = 10, $offset = 0, $search = '', $role = '') {
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('full_name', $search);
            $this->db->or_like('email', $search);
            $this->db->or_like('username', $search);
            $this->db->group_end();
        }
        
        if (!empty($role)) {
            $this->db->where('role', $role);
        }
        
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get('users')->result();
    }

    /**
     * Count users
     */
    public function count_users($search = '', $role = '') {
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('full_name', $search);
            $this->db->or_like('email', $search);
            $this->db->or_like('username', $search);
            $this->db->group_end();
        }
        
        if (!empty($role)) {
            $this->db->where('role', $role);
        }
        
        return $this->db->count_all_results('users');
    }

    /**
     * Update last login
     */
    public function update_last_login($user_id) {
        return $this->db->where('id', $user_id)->update('users', [
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
}
