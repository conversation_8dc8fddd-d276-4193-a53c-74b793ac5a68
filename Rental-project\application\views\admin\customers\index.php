<!-- <PERSON>er -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Manajemen Pelanggan</h2>
        <p class="text-muted mb-0">Kelola data pelanggan dan riwayat rental</p>
    </div>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
        <i class="fas fa-plus me-2"></i>Tambah Pelanggan
    </button>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= number_format($total_customers) ?></div>
                    <div>Total Pelanggan</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">0</div>
                    <div>Pelanggan Aktif</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">0</div>
                    <div>Pelanggan Baru</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number">0</div>
                    <div>VIP Members</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-crown"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('admin/customers') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Cari Pelanggan</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($search) ?>" placeholder="Nama, email, username...">
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Semua Status</option>
                    <option value="active">Aktif</option>
                    <option value="inactive">Tidak Aktif</option>
                    <option value="suspended">Suspended</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="sort" class="form-label">Urutkan</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="newest">Terbaru</option>
                    <option value="oldest">Terlama</option>
                    <option value="name">Nama A-Z</option>
                    <option value="most_rentals">Paling Sering Rental</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>Daftar Pelanggan
            <span class="badge bg-secondary ms-2"><?= number_format($total_customers) ?></span>
        </h5>
        
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportData('excel')">
                <i class="fas fa-file-excel me-1"></i>Excel
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printPage()">
                <i class="fas fa-print me-1"></i>Print
            </button>
        </div>
    </div>
    
    <div class="card-body">
        <?php if (empty($customers)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada pelanggan ditemukan</h5>
                <p class="text-muted">Silakan ubah filter pencarian atau tambah pelanggan baru</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                    <i class="fas fa-plus me-2"></i>Tambah Pelanggan Pertama
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="25%">Pelanggan</th>
                            <th width="20%">Kontak</th>
                            <th width="15%">Bergabung</th>
                            <th width="10%">Total Rental</th>
                            <th width="10%">Status</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = ($current_page - 1) * 10 + 1;
                        foreach ($customers as $customer): 
                        ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <?= strtoupper(substr($customer->full_name, 0, 2)) ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-1"><?= htmlspecialchars($customer->full_name) ?></h6>
                                            <small class="text-muted">@<?= htmlspecialchars($customer->username) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <i class="fas fa-envelope me-1"></i>
                                        <small><?= htmlspecialchars($customer->email) ?></small>
                                    </div>
                                    <?php if ($customer->phone): ?>
                                        <div>
                                            <i class="fas fa-phone me-1"></i>
                                            <small><?= htmlspecialchars($customer->phone) ?></small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= date('d M Y', strtotime($customer->created_at)) ?></small>
                                    <br><small class="text-muted"><?= date('H:i', strtotime($customer->created_at)) ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info">0 rental</span>
                                    <br><small class="text-muted">Rp 0 total</small>
                                </td>
                                <td>
                                    <?php
                                    $status_class = [
                                        'active' => 'success',
                                        'inactive' => 'secondary',
                                        'suspended' => 'danger'
                                    ];
                                    $status_text = [
                                        'active' => 'Aktif',
                                        'inactive' => 'Tidak Aktif',
                                        'suspended' => 'Suspended'
                                    ];
                                    ?>
                                    <span class="badge bg-<?= $status_class[$customer->status] ?>">
                                        <?= $status_text[$customer->status] ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" data-bs-target="#viewModal<?= $customer->id ?>"
                                                title="Lihat Detail">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" data-bs-target="#editModal<?= $customer->id ?>"
                                                title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="createRental(<?= $customer->id ?>)"
                                                title="Buat Rental">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- View Modal -->
                            <div class="modal fade" id="viewModal<?= $customer->id ?>" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Detail Pelanggan</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="text-center mb-3">
                                                <div class="avatar-circle-lg mx-auto mb-2">
                                                    <?= strtoupper(substr($customer->full_name, 0, 2)) ?>
                                                </div>
                                                <h5><?= htmlspecialchars($customer->full_name) ?></h5>
                                                <p class="text-muted">@<?= htmlspecialchars($customer->username) ?></p>
                                            </div>
                                            
                                            <table class="table table-sm">
                                                <tr><td>Email</td><td><?= htmlspecialchars($customer->email) ?></td></tr>
                                                <tr><td>Telepon</td><td><?= $customer->phone ?: '-' ?></td></tr>
                                                <tr><td>Alamat</td><td><?= $customer->address ?: '-' ?></td></tr>
                                                <tr><td>Status</td><td><span class="badge bg-<?= $status_class[$customer->status] ?>"><?= $status_text[$customer->status] ?></span></td></tr>
                                                <tr><td>Bergabung</td><td><?= date('d M Y H:i', strtotime($customer->created_at)) ?></td></tr>
                                                <tr><td>Update Terakhir</td><td><?= date('d M Y H:i', strtotime($customer->updated_at)) ?></td></tr>
                                            </table>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                            <button type="button" class="btn btn-primary" onclick="createRental(<?= $customer->id ?>)">
                                                <i class="fas fa-plus me-2"></i>Buat Rental
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Customer pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($current_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= base_url('admin/customers?page=' . ($current_page - 1) . '&search=' . urlencode($search)) ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                            <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                                <a class="page-link" href="<?= base_url('admin/customers?page=' . $i . '&search=' . urlencode($search)) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($current_page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= base_url('admin/customers?page=' . ($current_page + 1) . '&search=' . urlencode($search)) ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.avatar-circle-lg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 24px;
}
</style>

<script>
// Export functions
function exportData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open('<?= base_url('admin/customers/export') ?>?' + params.toString(), '_blank');
}

// Create rental for customer
function createRental(customerId) {
    window.location.href = '<?= base_url('admin/rentals/new') ?>?customer_id=' + customerId;
}

// Auto-submit form on filter change
$('#status, #sort').on('change', function() {
    $(this).closest('form').submit();
});

// Real-time search
let searchTimeout;
$('#search').on('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(function() {
        $('#search').closest('form').submit();
    }, 500);
});
</script>
