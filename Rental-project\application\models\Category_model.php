<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Category_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Get all categories
     */
    public function get_all($status = 'active') {
        if (!empty($status)) {
            $this->db->where('status', $status);
        }
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        
        return $this->db->get('categories')->result();
    }

    /**
     * Get categories with pagination
     */
    public function get_categories($limit = 10, $offset = 0, $search = '', $status = '') {
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('name', $search);
            $this->db->or_like('description', $search);
            $this->db->group_end();
        }
        
        if (!empty($status)) {
            $this->db->where('status', $status);
        }
        
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get('categories')->result();
    }

    /**
     * Count categories
     */
    public function count_categories($search = '', $status = '') {
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('name', $search);
            $this->db->or_like('description', $search);
            $this->db->group_end();
        }
        
        if (!empty($status)) {
            $this->db->where('status', $status);
        }
        
        return $this->db->count_all_results('categories');
    }

    /**
     * Get category by ID
     */
    public function get_by_id($id) {
        return $this->db->where('id', $id)->get('categories')->row();
    }

    /**
     * Get category by slug
     */
    public function get_by_slug($slug) {
        return $this->db->where('slug', $slug)->get('categories')->row();
    }

    /**
     * Create new category
     */
    public function create_category($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('categories', $data);
    }

    /**
     * Update category
     */
    public function update_category($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->where('id', $id)->update('categories', $data);
    }

    /**
     * Delete category
     */
    public function delete_category($id) {
        // Check if category has products
        $product_count = $this->db->where('category_id', $id)->count_all_results('products');
        
        if ($product_count > 0) {
            return false; // Cannot delete category with products
        }
        
        return $this->db->where('id', $id)->delete('categories');
    }

    /**
     * Check if slug exists
     */
    public function slug_exists($slug, $exclude_id = null) {
        $this->db->where('slug', $slug);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get('categories')->num_rows() > 0;
    }

    /**
     * Generate unique slug
     */
    public function generate_slug($name, $exclude_id = null) {
        $slug = url_title($name, 'dash', TRUE);
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slug_exists($slug, $exclude_id)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Get categories with product count
     */
    public function get_with_product_count($status = 'active') {
        $this->db->select('c.*, COUNT(p.id) as product_count');
        $this->db->from('categories c');
        $this->db->join('products p', 'p.category_id = c.id AND p.status = "active"', 'left');
        
        if (!empty($status)) {
            $this->db->where('c.status', $status);
        }
        
        $this->db->group_by('c.id');
        $this->db->order_by('c.sort_order', 'ASC');
        $this->db->order_by('c.name', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Update sort order
     */
    public function update_sort_order($id, $sort_order) {
        return $this->db->where('id', $id)->update('categories', [
            'sort_order' => $sort_order,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get next sort order
     */
    public function get_next_sort_order() {
        $this->db->select_max('sort_order');
        $result = $this->db->get('categories')->row();
        
        return ($result->sort_order ?? 0) + 1;
    }

    /**
     * Get category statistics
     */
    public function get_category_stats() {
        $stats = [];
        
        // Total categories
        $stats['total_categories'] = $this->db->where('status', 'active')->count_all_results('categories');
        
        // Categories with most products
        $this->db->select('c.name, COUNT(p.id) as product_count');
        $this->db->from('categories c');
        $this->db->join('products p', 'p.category_id = c.id AND p.status = "active"', 'left');
        $this->db->where('c.status', 'active');
        $this->db->group_by('c.id');
        $this->db->order_by('product_count', 'DESC');
        $this->db->limit(5);
        $stats['most_products'] = $this->db->get()->result();
        
        // Categories with most rentals
        $this->db->select('c.name, COUNT(ri.id) as rental_count');
        $this->db->from('categories c');
        $this->db->join('products p', 'p.category_id = c.id AND p.status = "active"', 'left');
        $this->db->join('rental_items ri', 'ri.product_id = p.id', 'left');
        $this->db->join('rentals r', 'r.id = ri.rental_id AND r.rental_status != "cancelled"', 'left');
        $this->db->where('c.status', 'active');
        $this->db->group_by('c.id');
        $this->db->order_by('rental_count', 'DESC');
        $this->db->limit(5);
        $stats['most_rented'] = $this->db->get()->result();
        
        return $stats;
    }

    /**
     * Search categories
     */
    public function search_categories($query, $limit = 10) {
        $this->db->where('status', 'active');
        $this->db->group_start();
        $this->db->like('name', $query);
        $this->db->or_like('description', $query);
        $this->db->group_end();
        
        $this->db->order_by('name', 'ASC');
        $this->db->limit($limit);
        
        return $this->db->get('categories')->result();
    }
}
