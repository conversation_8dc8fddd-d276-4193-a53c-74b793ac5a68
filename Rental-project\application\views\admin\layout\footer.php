        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }
        
        // Restore sidebar state
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
            }
        });
        
        // Mobile sidebar toggle
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !sidebarToggle.contains(e.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
        
        // Confirm delete actions
        function confirmDelete(message = 'Apakah Anda yakin ingin menghapus item ini?') {
            return confirm(message);
        }
        
        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(amount);
        }
        
        // Format number
        function formatNumber(number) {
            return new Intl.NumberFormat('id-ID').format(number);
        }
        
        // AJAX helper function
        function ajaxRequest(url, data, successCallback, errorCallback) {
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        if (successCallback) successCallback(response);
                    } else {
                        if (errorCallback) errorCallback(response);
                        else alert(response.message || 'Terjadi kesalahan');
                    }
                },
                error: function() {
                    if (errorCallback) errorCallback();
                    else alert('Terjadi kesalahan koneksi');
                }
            });
        }
        
        // Show loading state
        function showLoading(element) {
            const originalText = element.html();
            element.data('original-text', originalText);
            element.html('<i class="fas fa-spinner fa-spin me-2"></i>Memproses...');
            element.prop('disabled', true);
        }
        
        // Hide loading state
        function hideLoading(element) {
            const originalText = element.data('original-text');
            element.html(originalText);
            element.prop('disabled', false);
        }
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
        
        // DataTable helper (if using DataTables)
        function initDataTable(selector, options = {}) {
            const defaultOptions = {
                responsive: true,
                pageLength: 10,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/id.json'
                }
            };
            
            return $(selector).DataTable($.extend(defaultOptions, options));
        }
        
        // Form validation helper
        function validateForm(formSelector) {
            const form = $(formSelector);
            let isValid = true;
            
            form.find('[required]').each(function() {
                const field = $(this);
                const value = field.val().trim();
                
                if (!value) {
                    field.addClass('is-invalid');
                    isValid = false;
                } else {
                    field.removeClass('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Real-time search
        function setupSearch(inputSelector, targetSelector, searchUrl) {
            let searchTimeout;
            
            $(inputSelector).on('input', function() {
                const query = $(this).val();
                
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    if (query.length >= 2 || query.length === 0) {
                        $.get(searchUrl, { search: query }, function(data) {
                            $(targetSelector).html(data);
                        });
                    }
                }, 300);
            });
        }
        
        // File upload preview
        function previewImage(input, previewSelector) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    $(previewSelector).attr('src', e.target.result).show();
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const toast = new bootstrap.Toast(document.querySelector('.toast'));
                toast.show();
            });
        }
        
        // Export data
        function exportData(url, format = 'excel') {
            window.open(url + '?format=' + format, '_blank');
        }
        
        // Print page
        function printPage() {
            window.print();
        }
        
        // Refresh page data
        function refreshData() {
            location.reload();
        }
        
        // Check for updates (polling)
        function startPolling(url, interval = 30000) {
            setInterval(function() {
                $.get(url, function(data) {
                    // Handle updates
                    if (data.hasUpdates) {
                        // Show notification or update UI
                    }
                });
            }, interval);
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save (prevent default and trigger save)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const saveBtn = document.querySelector('[type="submit"], .btn-save');
                if (saveBtn) saveBtn.click();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
                if (modal) modal.hide();
            }
        });
        
        // Auto-save form data to localStorage
        function autoSaveForm(formSelector, key) {
            const form = $(formSelector);
            
            // Load saved data
            const savedData = localStorage.getItem(key);
            if (savedData) {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(function(name) {
                    form.find('[name="' + name + '"]').val(data[name]);
                });
            }
            
            // Save on change
            form.on('input change', function() {
                const formData = {};
                form.find('[name]').each(function() {
                    formData[$(this).attr('name')] = $(this).val();
                });
                localStorage.setItem(key, JSON.stringify(formData));
            });
            
            // Clear on submit
            form.on('submit', function() {
                localStorage.removeItem(key);
            });
        }
    </script>
</body>
</html>
