        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fab fa-playstation me-2"></i>PlayStation Rental Pro
                    </h5>
                    <p>Layanan rental PlayStation terpercaya dengan kualitas terbaik dan harga terjangkau.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-whatsapp"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Layanan</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= base_url('customer/products?category=1') ?>" class="text-light-50">Rental PS5</a></li>
                        <li><a href="<?= base_url('customer/products?category=1') ?>" class="text-light-50">Rental PS4</a></li>
                        <li><a href="<?= base_url('customer/products?category=1') ?>" class="text-light-50">Rental PS3</a></li>
                        <li><a href="<?= base_url('customer/products?category=3') ?>" class="text-light-50">TV & Monitor</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Akun</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= base_url('customer/dashboard') ?>" class="text-light-50">Dashboard</a></li>
                        <li><a href="<?= base_url('customer/profile') ?>" class="text-light-50">Profil</a></li>
                        <li><a href="<?= base_url('customer/rentals') ?>" class="text-light-50">Rental Saya</a></li>
                        <li><a href="<?= base_url('customer/change_password') ?>" class="text-light-50">Ubah Password</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Bantuan</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= base_url('home/contact') ?>" class="text-light-50">Kontak</a></li>
                        <li><a href="<?= base_url('home/terms') ?>" class="text-light-50">Syarat & Ketentuan</a></li>
                        <li><a href="<?= base_url('home/privacy') ?>" class="text-light-50">Kebijakan Privasi</a></li>
                        <li><a href="#" class="text-light-50">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Kontak Info</h6>
                    <p><i class="fas fa-map-marker-alt me-2"></i>Jakarta, Indonesia</p>
                    <p><i class="fas fa-phone me-2"></i>+62 812-3456-7890</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 PlayStation Rental Pro. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Made with <i class="fas fa-heart text-danger"></i> for gamers</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
        
        // Real-time search
        let searchTimeout;
        $('#searchInput').on('input', function() {
            const query = $(this).val();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(function() {
                    performSearch(query);
                }, 300);
            } else {
                $('#searchResults').hide();
            }
        });
        
        function performSearch(query) {
            $.get('<?= base_url('customer/search') ?>', { q: query }, function(data) {
                const results = JSON.parse(data);
                displaySearchResults(results);
            });
        }
        
        function displaySearchResults(results) {
            const resultsContainer = $('#searchResults');
            
            if (results.length === 0) {
                resultsContainer.html('<div class="search-result-item text-muted">Tidak ada produk ditemukan</div>');
            } else {
                let html = '';
                results.forEach(function(product) {
                    html += `
                        <div class="search-result-item" onclick="window.location.href='${product.url}'">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">${product.name}</h6>
                                    <small class="text-muted">${product.brand} - ${product.category}</small>
                                </div>
                                <div class="text-end">
                                    <small class="text-primary fw-bold">Rp ${formatNumber(product.price)}/hari</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                resultsContainer.html(html);
            }
            
            resultsContainer.show();
        }
        
        // Hide search results when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-box').length) {
                $('#searchResults').hide();
            }
        });
        
        // Format number with thousand separators
        function formatNumber(number) {
            return new Intl.NumberFormat('id-ID').format(number);
        }
        
        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(amount);
        }
        
        // Add to cart function
        function addToCart(productId, quantity = 1) {
            $.post('<?= base_url('customer/add_to_cart') ?>', {
                product_id: productId,
                quantity: quantity
            }, function(response) {
                const data = JSON.parse(response);
                if (data.success) {
                    // Show success message
                    showToast('Produk berhasil ditambahkan ke keranjang', 'success');
                    updateCartCount();
                } else {
                    showToast(data.message, 'error');
                }
            });
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            // Create toast container if not exists
            if (!$('#toastContainer').length) {
                $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
            }
            
            const $toast = $(toastHtml);
            $('#toastContainer').append($toast);
            
            const toast = new bootstrap.Toast($toast[0]);
            toast.show();
            
            // Remove toast element after it's hidden
            $toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
        
        // Check availability function
        function checkAvailability(productId, startDate, endDate, quantity = 1) {
            return new Promise((resolve, reject) => {
                $.post('<?= base_url('customer/check_availability') ?>', {
                    product_id: productId,
                    start_date: startDate,
                    end_date: endDate,
                    quantity: quantity
                }, function(response) {
                    const data = JSON.parse(response);
                    resolve(data);
                }).fail(function() {
                    reject('Terjadi kesalahan saat mengecek ketersediaan');
                });
            });
        }
        
        // Update cart count (placeholder)
        function updateCartCount() {
            // Will implement when cart system is ready
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    </script>
</body>
</html>
