<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('customer/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Produk</li>
    </ol>
</nav>

<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-1">Jelajahi Produk</h2>
                <p class="text-muted mb-0">Temukan PlayStation dan aksesori gaming terbaik untuk disewa</p>
            </div>
            <div class="text-end">
                <span class="badge bg-primary fs-6"><?= number_format($total_products) ?> Produk</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters & Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('customer/products') ?>" class="row g-3">
            <div class="col-lg-4 col-md-6">
                <label for="search" class="form-label">Cari Produk</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= htmlspecialchars($search) ?>" placeholder="PlayStation, TV, Controller...">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <label for="category" class="form-label">Kategori</label>
                <select class="form-select" id="category" name="category">
                    <option value="">Semua Kategori</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category->id ?>" <?= $selected_category == $category->id ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category->name) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <label for="sort" class="form-label">Urutkan</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="newest" <?= $sort == 'newest' ? 'selected' : '' ?>>Terbaru</option>
                    <option value="price_low" <?= $sort == 'price_low' ? 'selected' : '' ?>>Harga Terendah</option>
                    <option value="price_high" <?= $sort == 'price_high' ? 'selected' : '' ?>>Harga Tertinggi</option>
                    <option value="popular" <?= $sort == 'popular' ? 'selected' : '' ?>>Terpopuler</option>
                </select>
            </div>
            
            <div class="col-lg-2 col-md-6">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Category Quick Filter -->
<div class="mb-4">
    <div class="d-flex flex-wrap gap-2">
        <a href="<?= base_url('customer/products') ?>" 
           class="btn <?= empty($selected_category) ? 'btn-primary' : 'btn-outline-primary' ?> btn-sm">
            <i class="fas fa-th-large me-1"></i>Semua
        </a>
        <?php foreach ($categories as $category): ?>
            <a href="<?= base_url('customer/products?category=' . $category->id) ?>" 
               class="btn <?= $selected_category == $category->id ? 'btn-primary' : 'btn-outline-primary' ?> btn-sm">
                <?php
                $icons = [
                    'PlayStation Consoles' => 'fab fa-playstation',
                    'Controllers' => 'fas fa-gamepad',
                    'TVs & Monitors' => 'fas fa-tv',
                    'Accessories' => 'fas fa-headphones'
                ];
                $icon = $icons[$category->name] ?? 'fas fa-tag';
                ?>
                <i class="<?= $icon ?> me-1"></i><?= htmlspecialchars($category->name) ?>
            </a>
        <?php endforeach; ?>
    </div>
</div>

<!-- Products Grid -->
<?php if (empty($products)): ?>
    <div class="text-center py-5">
        <i class="fas fa-search fa-4x text-muted mb-4"></i>
        <h4 class="text-muted mb-3">Tidak ada produk ditemukan</h4>
        <p class="text-muted mb-4">Coba ubah filter pencarian atau kata kunci yang berbeda</p>
        <a href="<?= base_url('customer/products') ?>" class="btn btn-primary">
            <i class="fas fa-refresh me-2"></i>Reset Filter
        </a>
    </div>
<?php else: ?>
    <div class="row" data-aos="fade-up">
        <?php foreach ($products as $product): ?>
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="card product-card">
                    <!-- Product Image -->
                    <div class="product-image">
                        <?php if ($product->images): ?>
                            <?php $images = json_decode($product->images, true); ?>
                            <img src="<?= base_url('uploads/products/' . $images[0]) ?>" 
                                 alt="<?= htmlspecialchars($product->name) ?>" 
                                 class="img-fluid">
                        <?php else: ?>
                            <i class="placeholder-icon <?= $product->category_id == 1 ? 'fab fa-playstation' : ($product->category_id == 2 ? 'fas fa-gamepad' : 'fas fa-tv') ?>"></i>
                        <?php endif; ?>
                        
                        <!-- Condition Badge -->
                        <div class="position-absolute top-0 start-0 m-2">
                            <?php
                            $condition_colors = [
                                'new' => 'success',
                                'excellent' => 'primary',
                                'good' => 'info',
                                'fair' => 'warning'
                            ];
                            $condition_text = [
                                'new' => 'Baru',
                                'excellent' => 'Sangat Baik',
                                'good' => 'Baik',
                                'fair' => 'Cukup'
                            ];
                            ?>
                            <span class="badge bg-<?= $condition_colors[$product->condition_status] ?>">
                                <?= $condition_text[$product->condition_status] ?>
                            </span>
                        </div>
                        
                        <!-- Availability Badge -->
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Tersedia
                            </span>
                        </div>
                    </div>
                    
                    <!-- Product Info -->
                    <div class="card-body">
                        <div class="mb-2">
                            <span class="badge bg-secondary"><?= htmlspecialchars($product->category_name) ?></span>
                        </div>
                        
                        <h5 class="card-title mb-2"><?= htmlspecialchars($product->name) ?></h5>
                        
                        <?php if ($product->brand): ?>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-tag me-1"></i>
                                <?= htmlspecialchars($product->brand) ?>
                                <?= $product->model ? ' - ' . htmlspecialchars($product->model) : '' ?>
                            </p>
                        <?php endif; ?>
                        
                        <p class="card-text text-muted small mb-3">
                            <?= strlen($product->description) > 80 ? substr(htmlspecialchars($product->description), 0, 80) . '...' : htmlspecialchars($product->description) ?>
                        </p>
                        
                        <!-- Pricing -->
                        <div class="mb-3">
                            <div class="price-tag mb-2">
                                Rp <?= number_format($product->rental_price_daily) ?>/hari
                            </div>
                            
                            <?php if ($product->rental_price_hourly): ?>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Rp <?= number_format($product->rental_price_hourly) ?>/jam
                                </small>
                            <?php endif; ?>
                            
                            <?php if ($product->rental_price_weekly): ?>
                                <br><small class="text-muted">
                                    <i class="fas fa-calendar-week me-1"></i>
                                    Rp <?= number_format($product->rental_price_weekly) ?>/minggu
                                </small>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Features -->
                        <?php if ($product->features): ?>
                            <?php $features = json_decode($product->features, true); ?>
                            <div class="mb-3">
                                <?php foreach (array_slice($features, 0, 2) as $feature): ?>
                                    <span class="badge bg-light text-dark me-1 mb-1">
                                        <i class="fas fa-star me-1"></i><?= htmlspecialchars($feature) ?>
                                    </span>
                                <?php endforeach; ?>
                                <?php if (count($features) > 2): ?>
                                    <span class="badge bg-light text-dark">+<?= count($features) - 2 ?> lainnya</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('customer/product/' . $product->slug) ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-eye me-2"></i>Lihat Detail
                            </a>
                            
                            <button type="button" class="btn btn-outline-success btn-sm" 
                                    onclick="quickRent(<?= $product->id ?>)">
                                <i class="fas fa-calendar-plus me-1"></i>Rental Cepat
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <nav aria-label="Product pagination" class="mt-5">
            <ul class="pagination justify-content-center">
                <?php if ($current_page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= base_url('customer/products?page=' . ($current_page - 1) . '&search=' . urlencode($search) . '&category=' . $selected_category . '&sort=' . $sort) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                    <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                        <a class="page-link" href="<?= base_url('customer/products?page=' . $i . '&search=' . urlencode($search) . '&category=' . $selected_category . '&sort=' . $sort) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($current_page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= base_url('customer/products?page=' . ($current_page + 1) . '&search=' . urlencode($search) . '&category=' . $selected_category . '&sort=' . $sort) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    <?php endif; ?>
<?php endif; ?>

<!-- Quick Rent Modal -->
<div class="modal fade" id="quickRentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rental Cepat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickRentForm">
                    <input type="hidden" id="quickRentProductId" name="product_id">
                    
                    <div class="mb-3">
                        <label for="startDate" class="form-label">Tanggal Mulai</label>
                        <input type="date" class="form-control" id="startDate" name="start_date" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="endDate" class="form-label">Tanggal Selesai</label>
                        <input type="date" class="form-control" id="endDate" name="end_date" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Jumlah</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1" max="5">
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Fitur rental akan segera tersedia. Saat ini Anda dapat melihat detail produk terlebih dahulu.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" onclick="processQuickRent()">
                    <i class="fas fa-calendar-plus me-2"></i>Proses Rental
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-submit form on filter change
$('#category, #sort').on('change', function() {
    $(this).closest('form').submit();
});

// Quick rent function
function quickRent(productId) {
    $('#quickRentProductId').val(productId);
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    $('#startDate').attr('min', today).val(today);
    
    // Set minimum end date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    $('#endDate').attr('min', tomorrow.toISOString().split('T')[0]).val(tomorrow.toISOString().split('T')[0]);
    
    $('#quickRentModal').modal('show');
}

// Update end date minimum when start date changes
$('#startDate').on('change', function() {
    const startDate = new Date($(this).val());
    startDate.setDate(startDate.getDate() + 1);
    $('#endDate').attr('min', startDate.toISOString().split('T')[0]);
    
    if ($('#endDate').val() <= $(this).val()) {
        $('#endDate').val(startDate.toISOString().split('T')[0]);
    }
});

// Process quick rent
function processQuickRent() {
    const productId = $('#quickRentProductId').val();
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    const quantity = $('#quantity').val();
    
    if (!startDate || !endDate) {
        showToast('Mohon lengkapi tanggal rental', 'error');
        return;
    }
    
    // For now, just show info message
    showToast('Fitur rental akan segera tersedia. Terima kasih!', 'info');
    $('#quickRentModal').modal('hide');
}

// Real-time search
let searchTimeout;
$('#search').on('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(function() {
        $('#search').closest('form').submit();
    }, 800);
});
</script>
