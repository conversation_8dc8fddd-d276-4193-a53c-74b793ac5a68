<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->load->model(['User_model', 'Product_model', 'Category_model']);
        $this->load->helper(['url', 'form']);
        
        // Require admin access
        $this->auth_lib->require_admin();
    }

    /**
     * Admin Dashboard
     */
    public function index() {
        redirect('admin/dashboard');
    }

    /**
     * Dashboard
     */
    public function dashboard() {
        $data['title'] = 'Dashboard Admin - PlayStation Rental Pro';
        
        // Get dashboard statistics
        $data['stats'] = $this->get_dashboard_stats();
        
        // Get recent activities (will implement later)
        // $data['recent_activities'] = $this->get_recent_activities();
        
        $this->load->view('admin/layout/header', $data);
        $this->load->view('admin/dashboard', $data);
        $this->load->view('admin/layout/footer');
    }

    /**
     * Get dashboard statistics
     */
    private function get_dashboard_stats() {
        $stats = [];
        
        // Total users
        $stats['total_customers'] = $this->db->where('role', 'customer')->count_all_results('users');
        
        // Total products
        $stats['total_products'] = $this->db->where('status', 'active')->count_all_results('products');
        
        // Total categories
        $stats['total_categories'] = $this->db->where('status', 'active')->count_all_results('categories');
        
        // Total rentals (will implement later)
        $stats['total_rentals'] = 0; // $this->db->count_all_results('rentals');
        
        // Active rentals (will implement later)
        $stats['active_rentals'] = 0; // $this->db->where('rental_status', 'active')->count_all_results('rentals');
        
        // Pending rentals (will implement later)
        $stats['pending_rentals'] = 0; // $this->db->where('rental_status', 'pending')->count_all_results('rentals');
        
        // Revenue this month (will implement later)
        $stats['monthly_revenue'] = 0;
        
        // Revenue today (will implement later)
        $stats['daily_revenue'] = 0;
        
        return $stats;
    }

    /**
     * Products Management
     */
    public function products() {
        $data['title'] = 'Manajemen Produk - Admin';
        
        // Pagination
        $limit = 10;
        $offset = ($this->input->get('page') ?? 1 - 1) * $limit;
        $search = $this->input->get('search') ?? '';
        $category_id = $this->input->get('category') ?? '';
        
        // Get products
        $data['products'] = $this->Product_model->get_products($limit, $offset, $search, $category_id, '');
        $data['total_products'] = $this->Product_model->count_products($search, $category_id, '');
        
        // Get categories for filter
        $data['categories'] = $this->Category_model->get_all();
        
        // Pagination data
        $data['current_page'] = $this->input->get('page') ?? 1;
        $data['total_pages'] = ceil($data['total_products'] / $limit);
        $data['search'] = $search;
        $data['selected_category'] = $category_id;
        
        $this->load->view('admin/layout/header', $data);
        $this->load->view('admin/products/index', $data);
        $this->load->view('admin/layout/footer');
    }

    /**
     * Add Product
     */
    public function add_product() {
        $data['title'] = 'Tambah Produk - Admin';
        $data['categories'] = $this->Category_model->get_all();
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            
            $this->form_validation->set_rules('name', 'Nama Produk', 'required|trim|max_length[200]');
            $this->form_validation->set_rules('category_id', 'Kategori', 'required|integer');
            $this->form_validation->set_rules('description', 'Deskripsi', 'trim');
            $this->form_validation->set_rules('brand', 'Brand', 'trim|max_length[100]');
            $this->form_validation->set_rules('model', 'Model', 'trim|max_length[100]');
            $this->form_validation->set_rules('condition_status', 'Kondisi', 'required');
            $this->form_validation->set_rules('rental_price_daily', 'Harga Sewa Harian', 'required|decimal');
            $this->form_validation->set_rules('security_deposit', 'Deposit Keamanan', 'decimal');
            
            if ($this->form_validation->run()) {
                $product_data = [
                    'category_id' => $this->input->post('category_id'),
                    'name' => $this->input->post('name'),
                    'slug' => $this->Product_model->generate_slug($this->input->post('name')),
                    'description' => $this->input->post('description'),
                    'brand' => $this->input->post('brand'),
                    'model' => $this->input->post('model'),
                    'condition_status' => $this->input->post('condition_status'),
                    'rental_price_hourly' => $this->input->post('rental_price_hourly') ?: null,
                    'rental_price_daily' => $this->input->post('rental_price_daily'),
                    'rental_price_weekly' => $this->input->post('rental_price_weekly') ?: null,
                    'rental_price_monthly' => $this->input->post('rental_price_monthly') ?: null,
                    'security_deposit' => $this->input->post('security_deposit') ?: 0,
                    'replacement_cost' => $this->input->post('replacement_cost') ?: null,
                    'status' => 'active'
                ];
                
                // Handle specifications JSON
                $specifications = [];
                if ($this->input->post('spec_keys') && $this->input->post('spec_values')) {
                    $spec_keys = $this->input->post('spec_keys');
                    $spec_values = $this->input->post('spec_values');
                    
                    for ($i = 0; $i < count($spec_keys); $i++) {
                        if (!empty($spec_keys[$i]) && !empty($spec_values[$i])) {
                            $specifications[$spec_keys[$i]] = $spec_values[$i];
                        }
                    }
                }
                $product_data['specifications'] = !empty($specifications) ? json_encode($specifications) : null;
                
                // Handle features JSON
                $features = array_filter($this->input->post('features') ?: []);
                $product_data['features'] = !empty($features) ? json_encode($features) : null;
                
                // Handle included accessories JSON
                $accessories = array_filter($this->input->post('accessories') ?: []);
                $product_data['included_accessories'] = !empty($accessories) ? json_encode($accessories) : null;
                
                if ($this->Product_model->create_product($product_data)) {
                    $this->auth_lib->log_activity('create_product', 'products', $this->db->insert_id(), null, $product_data);
                    
                    $this->session->set_flashdata('success', 'Produk berhasil ditambahkan');
                    redirect('admin/products');
                } else {
                    $this->session->set_flashdata('error', 'Gagal menambahkan produk');
                }
            }
        }
        
        $this->load->view('admin/layout/header', $data);
        $this->load->view('admin/products/add', $data);
        $this->load->view('admin/layout/footer');
    }

    /**
     * Edit Product
     */
    public function edit_product($id) {
        $data['product'] = $this->Product_model->get_by_id($id);
        
        if (!$data['product']) {
            show_404();
        }
        
        $data['title'] = 'Edit Produk - Admin';
        $data['categories'] = $this->Category_model->get_all();
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            
            $this->form_validation->set_rules('name', 'Nama Produk', 'required|trim|max_length[200]');
            $this->form_validation->set_rules('category_id', 'Kategori', 'required|integer');
            $this->form_validation->set_rules('description', 'Deskripsi', 'trim');
            $this->form_validation->set_rules('brand', 'Brand', 'trim|max_length[100]');
            $this->form_validation->set_rules('model', 'Model', 'trim|max_length[100]');
            $this->form_validation->set_rules('condition_status', 'Kondisi', 'required');
            $this->form_validation->set_rules('rental_price_daily', 'Harga Sewa Harian', 'required|decimal');
            $this->form_validation->set_rules('security_deposit', 'Deposit Keamanan', 'decimal');
            
            if ($this->form_validation->run()) {
                $old_data = (array) $data['product'];
                
                $product_data = [
                    'category_id' => $this->input->post('category_id'),
                    'name' => $this->input->post('name'),
                    'description' => $this->input->post('description'),
                    'brand' => $this->input->post('brand'),
                    'model' => $this->input->post('model'),
                    'condition_status' => $this->input->post('condition_status'),
                    'rental_price_hourly' => $this->input->post('rental_price_hourly') ?: null,
                    'rental_price_daily' => $this->input->post('rental_price_daily'),
                    'rental_price_weekly' => $this->input->post('rental_price_weekly') ?: null,
                    'rental_price_monthly' => $this->input->post('rental_price_monthly') ?: null,
                    'security_deposit' => $this->input->post('security_deposit') ?: 0,
                    'replacement_cost' => $this->input->post('replacement_cost') ?: null,
                    'status' => $this->input->post('status')
                ];
                
                // Update slug if name changed
                if ($product_data['name'] !== $data['product']->name) {
                    $product_data['slug'] = $this->Product_model->generate_slug($product_data['name'], $id);
                }
                
                // Handle specifications JSON
                $specifications = [];
                if ($this->input->post('spec_keys') && $this->input->post('spec_values')) {
                    $spec_keys = $this->input->post('spec_keys');
                    $spec_values = $this->input->post('spec_values');
                    
                    for ($i = 0; $i < count($spec_keys); $i++) {
                        if (!empty($spec_keys[$i]) && !empty($spec_values[$i])) {
                            $specifications[$spec_keys[$i]] = $spec_values[$i];
                        }
                    }
                }
                $product_data['specifications'] = !empty($specifications) ? json_encode($specifications) : null;
                
                // Handle features JSON
                $features = array_filter($this->input->post('features') ?: []);
                $product_data['features'] = !empty($features) ? json_encode($features) : null;
                
                // Handle included accessories JSON
                $accessories = array_filter($this->input->post('accessories') ?: []);
                $product_data['included_accessories'] = !empty($accessories) ? json_encode($accessories) : null;
                
                if ($this->Product_model->update_product($id, $product_data)) {
                    $this->auth_lib->log_activity('update_product', 'products', $id, $old_data, $product_data);
                    
                    $this->session->set_flashdata('success', 'Produk berhasil diupdate');
                    redirect('admin/products');
                } else {
                    $this->session->set_flashdata('error', 'Gagal mengupdate produk');
                }
            }
        }
        
        $this->load->view('admin/layout/header', $data);
        $this->load->view('admin/products/edit', $data);
        $this->load->view('admin/layout/footer');
    }

    /**
     * Delete Product
     */
    public function delete_product($id) {
        $product = $this->Product_model->get_by_id($id);
        
        if (!$product) {
            $this->session->set_flashdata('error', 'Produk tidak ditemukan');
            redirect('admin/products');
        }
        
        // Check if product has rentals (will implement later)
        // $rental_count = $this->db->where('product_id', $id)->count_all_results('rental_items');
        // if ($rental_count > 0) {
        //     $this->session->set_flashdata('error', 'Tidak dapat menghapus produk yang memiliki riwayat rental');
        //     redirect('admin/products');
        // }
        
        if ($this->Product_model->delete_product($id)) {
            $this->auth_lib->log_activity('delete_product', 'products', $id, (array) $product, null);
            
            $this->session->set_flashdata('success', 'Produk berhasil dihapus');
        } else {
            $this->session->set_flashdata('error', 'Gagal menghapus produk');
        }
        
        redirect('admin/products');
    }

    /**
     * Categories Management
     */
    public function categories() {
        $data['title'] = 'Manajemen Kategori - Admin';
        
        // Pagination
        $limit = 10;
        $offset = ($this->input->get('page') ?? 1 - 1) * $limit;
        $search = $this->input->get('search') ?? '';
        
        // Get categories
        $data['categories'] = $this->Category_model->get_categories($limit, $offset, $search, '');
        $data['total_categories'] = $this->Category_model->count_categories($search, '');
        
        // Pagination data
        $data['current_page'] = $this->input->get('page') ?? 1;
        $data['total_pages'] = ceil($data['total_categories'] / $limit);
        $data['search'] = $search;
        
        $this->load->view('admin/layout/header', $data);
        $this->load->view('admin/categories/index', $data);
        $this->load->view('admin/layout/footer');
    }
}