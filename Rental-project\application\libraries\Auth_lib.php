<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth_lib {
    
    protected $CI;
    
    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->model('User_model');
    }
    
    /**
     * Check if user is logged in
     */
    public function is_logged_in() {
        return $this->CI->session->userdata('user_id') ? true : false;
    }
    
    /**
     * Get current user ID
     */
    public function get_user_id() {
        return $this->CI->session->userdata('user_id');
    }
    
    /**
     * Get current user data
     */
    public function get_user() {
        $user_id = $this->get_user_id();
        if ($user_id) {
            return $this->CI->User_model->get_by_id($user_id);
        }
        return false;
    }
    
    /**
     * Check if user has specific role
     */
    public function has_role($role) {
        $user_role = $this->CI->session->userdata('role');
        if (is_array($role)) {
            return in_array($user_role, $role);
        }
        return $user_role === $role;
    }
    
    /**
     * Check if user is admin
     */
    public function is_admin() {
        return $this->has_role('admin');
    }
    
    /**
     * Check if user is customer
     */
    public function is_customer() {
        return $this->has_role('customer');
    }
    
    /**
     * Check if user is staff
     */
    public function is_staff() {
        return $this->has_role('staff');
    }
    
    /**
     * Require login - redirect to login if not authenticated
     */
    public function require_login($redirect_url = '') {
        if (!$this->is_logged_in()) {
            if (empty($redirect_url)) {
                $redirect_url = current_url();
            }
            $this->CI->session->set_userdata('redirect_url', $redirect_url);
            redirect('auth/login');
        }
    }
    
    /**
     * Require specific role
     */
    public function require_role($role, $redirect_url = 'auth/login') {
        $this->require_login();
        
        if (!$this->has_role($role)) {
            $this->CI->session->set_flashdata('error', 'Anda tidak memiliki akses ke halaman tersebut');
            redirect($redirect_url);
        }
    }
    
    /**
     * Require admin access
     */
    public function require_admin() {
        $this->require_role('admin', 'auth/login');
    }
    
    /**
     * Require customer access
     */
    public function require_customer() {
        $this->require_role('customer', 'auth/login');
    }
    
    /**
     * Check remember me token and auto login
     */
    public function check_remember_token() {
        if (!$this->is_logged_in()) {
            $remember_token = $this->CI->input->cookie('remember_token');
            
            if ($remember_token) {
                $session_data = $this->CI->User_model->verify_session($remember_token);
                
                if ($session_data) {
                    // Auto login user
                    $user_session = [
                        'user_id' => $session_data->user_id,
                        'username' => $session_data->username,
                        'email' => $session_data->email,
                        'full_name' => $session_data->full_name,
                        'role' => $session_data->role,
                        'logged_in' => TRUE
                    ];
                    $this->CI->session->set_userdata($user_session);
                    
                    // Update last login
                    $this->CI->User_model->update_last_login($session_data->user_id);
                    
                    return true;
                } else {
                    // Invalid token, delete cookie
                    delete_cookie('remember_token');
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get user permissions (for future use)
     */
    public function get_permissions() {
        $role = $this->CI->session->userdata('role');
        
        $permissions = [];
        
        switch ($role) {
            case 'admin':
                $permissions = [
                    'manage_users',
                    'manage_products',
                    'manage_rentals',
                    'manage_payments',
                    'view_reports',
                    'manage_settings',
                    'manage_deliveries',
                    'manage_promotions'
                ];
                break;
                
            case 'staff':
                $permissions = [
                    'manage_rentals',
                    'manage_deliveries',
                    'view_products',
                    'process_payments'
                ];
                break;
                
            case 'customer':
                $permissions = [
                    'view_products',
                    'create_rental',
                    'view_own_rentals',
                    'make_payments'
                ];
                break;
        }
        
        return $permissions;
    }
    
    /**
     * Check if user has specific permission
     */
    public function has_permission($permission) {
        $permissions = $this->get_permissions();
        return in_array($permission, $permissions);
    }
    
    /**
     * Generate CSRF token
     */
    public function generate_csrf_token() {
        $token = bin2hex(random_bytes(32));
        $this->CI->session->set_userdata('csrf_token', $token);
        return $token;
    }
    
    /**
     * Verify CSRF token
     */
    public function verify_csrf_token($token) {
        $session_token = $this->CI->session->userdata('csrf_token');
        return hash_equals($session_token, $token);
    }
    
    /**
     * Log user activity
     */
    public function log_activity($action, $table_name = null, $record_id = null, $old_values = null, $new_values = null) {
        $data = [
            'user_id' => $this->get_user_id(),
            'action' => $action,
            'table_name' => $table_name,
            'record_id' => $record_id,
            'old_values' => $old_values ? json_encode($old_values) : null,
            'new_values' => $new_values ? json_encode($new_values) : null,
            'ip_address' => $this->CI->input->ip_address(),
            'user_agent' => $this->CI->input->user_agent(),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->CI->db->insert('activity_logs', $data);
    }
    
    /**
     * Clean up expired sessions
     */
    public function cleanup_sessions() {
        $this->CI->User_model->cleanup_expired_sessions();
    }
    
    /**
     * Force logout user
     */
    public function force_logout($user_id) {
        // Delete all sessions for user
        $this->CI->db->where('user_id', $user_id)->delete('user_sessions');
        
        // If current user, destroy session
        if ($this->get_user_id() == $user_id) {
            $this->CI->session->sess_destroy();
        }
    }
    
    /**
     * Get login redirect URL
     */
    public function get_login_redirect() {
        $redirect_url = $this->CI->session->userdata('redirect_url');
        
        if ($redirect_url) {
            $this->CI->session->unset_userdata('redirect_url');
            return $redirect_url;
        }
        
        // Default redirect based on role
        $role = $this->CI->session->userdata('role');
        return $role === 'admin' ? 'admin/dashboard' : 'customer/dashboard';
    }
}
