<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .register-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .playstation-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .text-link {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .text-link:hover {
            color: #764ba2;
        }
        
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-container">
                    <div class="register-header">
                        <i class="fab fa-playstation playstation-icon"></i>
                        <h2 class="mb-0">PlayStation Rental</h2>
                        <p class="mb-0">Daftar akun baru</p>
                    </div>
                    
                    <div class="register-body">
                        <?php if ($this->session->flashdata('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= $this->session->flashdata('success') ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($this->session->flashdata('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?= $this->session->flashdata('error') ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?= validation_errors('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>', '</div>') ?>
                        
                        <form id="registerForm" method="post" action="<?= base_url('auth/register') ?>">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>Username
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?= set_value('username') ?>" required>
                                    <div class="form-text">Minimal 3 karakter, hanya huruf dan angka</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= set_value('email') ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="full_name" class="form-label">
                                    <i class="fas fa-id-card me-2"></i>Nama Lengkap
                                </label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?= set_value('full_name') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-2"></i>Nomor Telepon
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= set_value('phone') ?>" placeholder="Contoh: 08123456789">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <div class="form-text">Minimal 6 karakter</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirm" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Konfirmasi Password
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password_confirm" name="password_confirm" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePasswordConfirm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text" id="passwordMatch"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    Saya setuju dengan <a href="#" class="text-link">Syarat & Ketentuan</a> 
                                    dan <a href="#" class="text-link">Kebijakan Privasi</a>
                                </label>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-register">
                                    <i class="fas fa-user-plus me-2"></i>Daftar
                                </button>
                            </div>
                            
                            <div class="text-center">
                                <p class="mb-0">Sudah punya akun?</p>
                                <a href="<?= base_url('auth/login') ?>" class="text-link fw-bold">
                                    <i class="fas fa-sign-in-alt me-1"></i>Masuk sekarang
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('#togglePassword').click(function() {
                togglePasswordField('#password', $(this));
            });
            
            $('#togglePasswordConfirm').click(function() {
                togglePasswordField('#password_confirm', $(this));
            });
            
            function togglePasswordField(fieldId, button) {
                const passwordField = $(fieldId);
                const icon = button.find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            }
            
            // Password strength checker
            $('#password').on('input', function() {
                const password = $(this).val();
                const strengthBar = $('#passwordStrength');
                
                if (password.length === 0) {
                    strengthBar.removeClass().addClass('password-strength');
                    return;
                }
                
                let strength = 0;
                if (password.length >= 6) strength++;
                if (password.match(/[a-z]/)) strength++;
                if (password.match(/[A-Z]/)) strength++;
                if (password.match(/[0-9]/)) strength++;
                if (password.match(/[^a-zA-Z0-9]/)) strength++;
                
                strengthBar.removeClass();
                if (strength <= 2) {
                    strengthBar.addClass('password-strength strength-weak');
                } else if (strength <= 3) {
                    strengthBar.addClass('password-strength strength-medium');
                } else {
                    strengthBar.addClass('password-strength strength-strong');
                }
            });
            
            // Password match checker
            $('#password_confirm').on('input', function() {
                const password = $('#password').val();
                const confirmPassword = $(this).val();
                const matchText = $('#passwordMatch');
                
                if (confirmPassword.length === 0) {
                    matchText.text('');
                    return;
                }
                
                if (password === confirmPassword) {
                    matchText.text('✓ Password cocok').css('color', '#28a745');
                } else {
                    matchText.text('✗ Password tidak cocok').css('color', '#dc3545');
                }
            });
            
            // AJAX form submission
            $('#registerForm').submit(function(e) {
                e.preventDefault();
                
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                
                // Show loading
                submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Memproses...');
                submitBtn.prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                              '<i class="fas fa-check-circle me-2"></i>' + 
                              response.message +
                              '</div>').prependTo('.register-body');
                            
                            // Redirect after delay
                            setTimeout(function() {
                                window.location.href = '<?= base_url("auth/login") ?>';
                            }, 2000);
                        } else {
                            // Show error message
                            $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                              '<i class="fas fa-exclamation-circle me-2"></i>' + 
                              response.message +
                              '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                              '</div>').prependTo('.register-body');
                            
                            // Reset button
                            submitBtn.html(originalText);
                            submitBtn.prop('disabled', false);
                        }
                    },
                    error: function() {
                        // Show error message
                        $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                          '<i class="fas fa-exclamation-circle me-2"></i>' + 
                          'Terjadi kesalahan. Silakan coba lagi.' +
                          '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                          '</div>').prependTo('.register-body');
                        
                        // Reset button
                        submitBtn.html(originalText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });
            
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>
</html>
