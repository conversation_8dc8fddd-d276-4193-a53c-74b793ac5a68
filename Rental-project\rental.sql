-- =============================================
-- Database: PlayStation Rental Management System
-- Version: 1.0
-- Created: 2025-08-30
-- =============================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- =============================================
-- 1. USERS & AUTHENTICATION TABLES
-- =============================================

-- Users table (Admin & Customers)
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `role` enum('admin','customer','staff') NOT NULL DEFAULT 'customer',
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `email_verified` tinyint(1) DEFAULT 0,
  `profile_image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions for security
CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`session_token`),
  KEY `idx_expires` (`expires_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password reset tokens
CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_token` (`token`),
  KEY `idx_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. PRODUCT & INVENTORY MANAGEMENT
-- =============================================

-- Product categories
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL UNIQUE,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Products (PS3, PS4, PS5, TV, Controllers, etc.)
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL UNIQUE,
  `description` text DEFAULT NULL,
  `specifications` json DEFAULT NULL,
  `brand` varchar(100) DEFAULT NULL,
  `model` varchar(100) DEFAULT NULL,
  `condition_status` enum('new','excellent','good','fair') NOT NULL DEFAULT 'excellent',
  `rental_price_hourly` decimal(10,2) DEFAULT NULL,
  `rental_price_daily` decimal(10,2) NOT NULL,
  `rental_price_weekly` decimal(10,2) DEFAULT NULL,
  `rental_price_monthly` decimal(10,2) DEFAULT NULL,
  `security_deposit` decimal(10,2) NOT NULL DEFAULT 0,
  `replacement_cost` decimal(10,2) DEFAULT NULL,
  `images` json DEFAULT NULL,
  `features` json DEFAULT NULL,
  `included_accessories` json DEFAULT NULL,
  `status` enum('active','inactive','maintenance') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_brand` (`brand`),
  FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inventory tracking for each product unit
CREATE TABLE `inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `serial_number` varchar(100) UNIQUE DEFAULT NULL,
  `unit_code` varchar(50) NOT NULL UNIQUE,
  `purchase_date` date DEFAULT NULL,
  `purchase_price` decimal(10,2) DEFAULT NULL,
  `warranty_expires` date DEFAULT NULL,
  `condition_notes` text DEFAULT NULL,
  `maintenance_schedule` json DEFAULT NULL,
  `last_maintenance` date DEFAULT NULL,
  `next_maintenance` date DEFAULT NULL,
  `location` varchar(100) DEFAULT 'warehouse',
  `status` enum('available','rented','maintenance','damaged','retired') NOT NULL DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_unit_code` (`unit_code`),
  KEY `idx_serial` (`serial_number`),
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 3. RENTAL & BOOKING SYSTEM
-- =============================================

-- Rental bookings
CREATE TABLE `rentals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rental_code` varchar(20) NOT NULL UNIQUE,
  `customer_id` int(11) NOT NULL,
  `rental_type` enum('pickup','delivery','setup') NOT NULL DEFAULT 'pickup',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `actual_return_date` datetime DEFAULT NULL,
  `total_days` int(11) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `security_deposit` decimal(10,2) NOT NULL DEFAULT 0,
  `delivery_fee` decimal(10,2) DEFAULT 0,
  `setup_fee` decimal(10,2) DEFAULT 0,
  `late_fee` decimal(10,2) DEFAULT 0,
  `damage_fee` decimal(10,2) DEFAULT 0,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','partial','paid','refunded','cancelled') NOT NULL DEFAULT 'pending',
  `rental_status` enum('pending','confirmed','active','completed','cancelled','overdue') NOT NULL DEFAULT 'pending',
  `delivery_address` text DEFAULT NULL,
  `delivery_notes` text DEFAULT NULL,
  `setup_required` tinyint(1) DEFAULT 0,
  `pickup_time` datetime DEFAULT NULL,
  `delivery_time` datetime DEFAULT NULL,
  `return_time` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_rental_code` (`rental_code`),
  KEY `idx_dates` (`start_date`, `end_date`),
  KEY `idx_status` (`rental_status`),
  KEY `idx_payment_status` (`payment_status`),
  FOREIGN KEY (`customer_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Rental items (products in each rental)
CREATE TABLE `rental_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rental_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `inventory_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `condition_out` text DEFAULT NULL,
  `condition_in` text DEFAULT NULL,
  `damage_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_rental` (`rental_id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_inventory` (`inventory_id`),
  FOREIGN KEY (`rental_id`) REFERENCES `rentals`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`inventory_id`) REFERENCES `inventory`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 4. PAYMENT SYSTEM
-- =============================================

-- Payment methods
CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('bank_transfer','e_wallet','qris','credit_card','cash') NOT NULL,
  `provider` varchar(100) DEFAULT NULL,
  `account_info` json DEFAULT NULL,
  `fees` json DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payment transactions
CREATE TABLE `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rental_id` int(11) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `transaction_id` varchar(100) UNIQUE DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_type` enum('rental','deposit','late_fee','damage_fee','refund') NOT NULL DEFAULT 'rental',
  `status` enum('pending','processing','completed','failed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `gateway_response` json DEFAULT NULL,
  `proof_image` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_rental` (`rental_id`),
  KEY `idx_payment_method` (`payment_method_id`),
  KEY `idx_transaction` (`transaction_id`),
  KEY `idx_status` (`status`),
  KEY `idx_processed_by` (`processed_by`),
  FOREIGN KEY (`rental_id`) REFERENCES `rentals`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 5. NOTIFICATION SYSTEM
-- =============================================

-- Notification templates
CREATE TABLE `notification_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('email','sms','whatsapp','push') NOT NULL,
  `trigger_event` varchar(100) NOT NULL,
  `subject` varchar(200) DEFAULT NULL,
  `content` text NOT NULL,
  `variables` json DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_trigger` (`trigger_event`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notification queue
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `type` enum('email','sms','whatsapp','push','system') NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `subject` varchar(200) DEFAULT NULL,
  `message` text NOT NULL,
  `data` json DEFAULT NULL,
  `status` enum('pending','sent','failed','cancelled') NOT NULL DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `retry_count` int(11) DEFAULT 0,
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_template` (`template_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_scheduled` (`scheduled_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`template_id`) REFERENCES `notification_templates`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 6. DELIVERY & SETUP MANAGEMENT
-- =============================================

-- Delivery areas and pricing
CREATE TABLE `delivery_areas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `postal_codes` json DEFAULT NULL,
  `delivery_fee` decimal(10,2) NOT NULL DEFAULT 0,
  `setup_fee` decimal(10,2) NOT NULL DEFAULT 0,
  `min_delivery_time` int(11) NOT NULL DEFAULT 60,
  `max_delivery_time` int(11) NOT NULL DEFAULT 180,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Delivery staff
CREATE TABLE `delivery_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `vehicle_type` varchar(50) DEFAULT NULL,
  `vehicle_number` varchar(20) DEFAULT NULL,
  `license_number` varchar(50) DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `emergency_contact` varchar(100) DEFAULT NULL,
  `areas_covered` json DEFAULT NULL,
  `status` enum('active','inactive','busy') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Delivery tracking
CREATE TABLE `deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rental_id` int(11) NOT NULL,
  `staff_id` int(11) DEFAULT NULL,
  `type` enum('delivery','pickup','setup') NOT NULL,
  `scheduled_date` datetime NOT NULL,
  `actual_date` datetime DEFAULT NULL,
  `address` text NOT NULL,
  `contact_person` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `special_instructions` text DEFAULT NULL,
  `status` enum('scheduled','in_transit','completed','failed','cancelled') NOT NULL DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `signature_image` varchar(255) DEFAULT NULL,
  `photo_proof` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_rental` (`rental_id`),
  KEY `idx_staff` (`staff_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_scheduled` (`scheduled_date`),
  FOREIGN KEY (`rental_id`) REFERENCES `rentals`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`staff_id`) REFERENCES `delivery_staff`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 7. PROMOTIONS & CONTENT MANAGEMENT
-- =============================================

-- Promotions and discounts
CREATE TABLE `promotions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `code` varchar(50) UNIQUE DEFAULT NULL,
  `description` text DEFAULT NULL,
  `type` enum('percentage','fixed_amount','free_delivery','free_setup') NOT NULL,
  `value` decimal(10,2) NOT NULL,
  `min_rental_amount` decimal(10,2) DEFAULT NULL,
  `max_discount` decimal(10,2) DEFAULT NULL,
  `usage_limit` int(11) DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  `user_limit` int(11) DEFAULT 1,
  `applicable_products` json DEFAULT NULL,
  `applicable_categories` json DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('active','inactive','expired') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_dates` (`start_date`, `end_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Game library and popular games
CREATE TABLE `games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `platform` enum('PS3','PS4','PS5','Multi') NOT NULL,
  `genre` varchar(100) DEFAULT NULL,
  `rating` varchar(10) DEFAULT NULL,
  `release_date` date DEFAULT NULL,
  `description` text DEFAULT NULL,
  `cover_image` varchar(255) DEFAULT NULL,
  `trailer_url` varchar(255) DEFAULT NULL,
  `popularity_score` int(11) DEFAULT 0,
  `is_featured` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_genre` (`genre`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 8. MAINTENANCE & SECURITY
-- =============================================

-- Maintenance logs
CREATE TABLE `maintenance_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inventory_id` int(11) NOT NULL,
  `type` enum('routine','repair','cleaning','inspection') NOT NULL,
  `description` text NOT NULL,
  `cost` decimal(10,2) DEFAULT NULL,
  `performed_by` int(11) DEFAULT NULL,
  `scheduled_date` date DEFAULT NULL,
  `completed_date` date DEFAULT NULL,
  `next_maintenance` date DEFAULT NULL,
  `status` enum('scheduled','in_progress','completed','cancelled') NOT NULL DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_inventory` (`inventory_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_performed_by` (`performed_by`),
  FOREIGN KEY (`inventory_id`) REFERENCES `inventory`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`performed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) NOT NULL UNIQUE,
  `value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `category` varchar(50) DEFAULT 'general',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_key` (`key_name`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity logs for security
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(50) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_table` (`table_name`),
  KEY `idx_created` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 9. SAMPLE DATA INSERTION
-- =============================================

-- Insert default admin user (password: password)
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `role`, `status`, `email_verified`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', 'active', 1);

-- Insert sample customer users
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `phone`, `role`, `status`, `email_verified`) VALUES
('johndoe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', '08123456789', 'customer', 'active', 1),
('janesmith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane Smith', '08987654321', 'customer', 'active', 1);

-- Insert categories
INSERT INTO `categories` (`name`, `slug`, `description`, `status`) VALUES
('PlayStation Consoles', 'playstation-consoles', 'Gaming consoles PS3, PS4, PS5', 'active'),
('Controllers', 'controllers', 'Gaming controllers and accessories', 'active'),
('TVs & Monitors', 'tvs-monitors', 'Display devices for gaming', 'active'),
('Accessories', 'accessories', 'Gaming accessories and peripherals', 'active');

-- Insert payment methods
INSERT INTO `payment_methods` (`name`, `type`, `provider`, `status`) VALUES
('Bank Transfer BCA', 'bank_transfer', 'BCA', 'active'),
('Bank Transfer Mandiri', 'bank_transfer', 'Mandiri', 'active'),
('GoPay', 'e_wallet', 'Gojek', 'active'),
('OVO', 'e_wallet', 'OVO', 'active'),
('DANA', 'e_wallet', 'DANA', 'active'),
('QRIS', 'qris', 'Universal', 'active'),
('Cash', 'cash', 'Cash', 'active');

-- Insert delivery areas
INSERT INTO `delivery_areas` (`name`, `delivery_fee`, `setup_fee`, `status`) VALUES
('Jakarta Pusat', 25000, 50000, 'active'),
('Jakarta Selatan', 30000, 50000, 'active'),
('Jakarta Barat', 35000, 50000, 'active'),
('Jakarta Utara', 35000, 50000, 'active'),
('Jakarta Timur', 40000, 50000, 'active'),
('Tangerang', 50000, 75000, 'active'),
('Bekasi', 50000, 75000, 'active'),
('Depok', 45000, 75000, 'active'),
('Bogor', 60000, 100000, 'active');

-- Insert system settings
-- Insert sample products
INSERT INTO `products` (`category_id`, `name`, `slug`, `description`, `brand`, `model`, `condition_status`, `rental_price_daily`, `rental_price_weekly`, `rental_price_monthly`, `security_deposit`, `replacement_cost`, `specifications`, `features`, `included_accessories`, `status`) VALUES
(1, 'PlayStation 5 Standard Edition', 'playstation-5-standard-edition', 'Konsol gaming terbaru dari Sony dengan performa ultra-high speed SSD dan ray tracing', 'Sony', 'CFI-1216A', 'excellent', 150000, 900000, 3000000, 500000, 8000000, '{"storage":"825GB SSD","cpu":"AMD Zen 2","gpu":"AMD RDNA 2","ram":"16GB GDDR6"}', '["Ray Tracing","3D Audio","Haptic Feedback","Adaptive Triggers"]', '["DualSense Controller","HDMI Cable","Power Cable","USB Cable"]', 'active'),
(1, 'PlayStation 5 Digital Edition', 'playstation-5-digital-edition', 'PlayStation 5 versi digital tanpa disc drive dengan performa yang sama', 'Sony', 'CFI-1216B', 'excellent', 140000, 850000, 2800000, 450000, 7000000, '{"storage":"825GB SSD","cpu":"AMD Zen 2","gpu":"AMD RDNA 2","ram":"16GB GDDR6"}', '["Ray Tracing","3D Audio","Haptic Feedback","Adaptive Triggers","Digital Only"]', '["DualSense Controller","HDMI Cable","Power Cable","USB Cable"]', 'active'),
(1, 'PlayStation 4 Pro', 'playstation-4-pro', 'PlayStation 4 dengan performa enhanced untuk gaming 4K', 'Sony', 'CUH-7216B', 'excellent', 100000, 600000, 2000000, 300000, 5000000, '{"storage":"1TB HDD","cpu":"AMD Jaguar","gpu":"AMD Radeon","ram":"8GB GDDR5"}', '["4K Gaming","HDR Support","Boost Mode"]', '["DualShock 4 Controller","HDMI Cable","Power Cable","USB Cable"]', 'active'),
(1, 'PlayStation 4 Slim', 'playstation-4-slim', 'PlayStation 4 versi slim dengan desain yang lebih compact', 'Sony', 'CUH-2216B', 'good', 80000, 480000, 1600000, 250000, 4000000, '{"storage":"1TB HDD","cpu":"AMD Jaguar","gpu":"AMD Radeon","ram":"8GB GDDR5"}', '["HDR Support","Remote Play","Share Play"]', '["DualShock 4 Controller","HDMI Cable","Power Cable","USB Cable"]', 'active'),
(3, 'Smart TV 55 inch 4K', 'smart-tv-55-inch-4k', 'Smart TV 55 inch dengan resolusi 4K untuk pengalaman gaming terbaik', 'Samsung', 'UA55AU7700', 'excellent', 75000, 450000, 1500000, 200000, 8000000, '{"size":"55 inch","resolution":"4K UHD","refresh_rate":"60Hz","smart_tv":"Yes"}', '["4K UHD","Smart TV","HDR10+","Game Mode"]', '["Remote Control","Power Cable","HDMI Cable"]', 'active'),
(3, 'Gaming Monitor 27 inch', 'gaming-monitor-27-inch', 'Monitor gaming 27 inch dengan refresh rate tinggi', 'ASUS', 'VG27AQ', 'excellent', 50000, 300000, 1000000, 150000, 4000000, '{"size":"27 inch","resolution":"1440p","refresh_rate":"165Hz","panel":"IPS"}', '["165Hz Refresh Rate","1ms Response Time","G-Sync Compatible","HDR10"]', '["Power Cable","DisplayPort Cable","HDMI Cable"]', 'active'),
(2, 'DualSense Controller PS5', 'dualsense-controller-ps5', 'Controller wireless untuk PlayStation 5 dengan haptic feedback', 'Sony', 'CFI-ZCT1W', 'excellent', 25000, 150000, 500000, 50000, 800000, '{"connectivity":"Wireless","battery":"Built-in","features":"Haptic Feedback"}', '["Haptic Feedback","Adaptive Triggers","Built-in Microphone","Motion Sensors"]', '["USB-C Cable"]', 'active'),
(2, 'DualShock 4 Controller', 'dualshock-4-controller', 'Controller wireless untuk PlayStation 4', 'Sony', 'CUH-ZCT2U', 'good', 20000, 120000, 400000, 40000, 600000, '{"connectivity":"Wireless","battery":"Built-in","features":"Touchpad"}', '["Touchpad","Light Bar","Built-in Speaker","Motion Sensors"]', '["USB Cable"]', 'active');

-- Insert sample inventory
INSERT INTO `inventory` (`product_id`, `unit_code`, `serial_number`, `purchase_date`, `condition_notes`, `location`, `status`) VALUES
(1, 'PS5-001', 'PS5001234567', '2024-01-15', 'Kondisi sangat baik, rutin maintenance', 'warehouse', 'available'),
(1, 'PS5-002', 'PS5001234568', '2024-01-15', 'Kondisi sangat baik, rutin maintenance', 'warehouse', 'available'),
(2, 'PS5D-001', 'PS5D01234567', '2024-02-01', 'Kondisi sangat baik', 'warehouse', 'available'),
(3, 'PS4P-001', 'PS4P01234567', '2023-06-10', 'Kondisi baik, sudah maintenance', 'warehouse', 'available'),
(3, 'PS4P-002', 'PS4P01234568', '2023-06-10', 'Kondisi baik, sudah maintenance', 'warehouse', 'available'),
(4, 'PS4S-001', 'PS4S01234567', '2023-03-15', 'Kondisi baik', 'warehouse', 'available'),
(5, 'TV55-001', 'TV5501234567', '2024-01-20', 'Kondisi sangat baik', 'warehouse', 'available'),
(5, 'TV55-002', 'TV5501234568', '2024-01-20', 'Kondisi sangat baik', 'warehouse', 'available'),
(6, 'MON27-001', 'MON2701234567', '2024-02-10', 'Kondisi sangat baik', 'warehouse', 'available'),
(7, 'DS5-001', 'DS501234567', '2024-01-15', 'Kondisi sangat baik', 'warehouse', 'available'),
(7, 'DS5-002', 'DS501234568', '2024-01-15', 'Kondisi sangat baik', 'warehouse', 'available'),
(7, 'DS5-003', 'DS501234569', '2024-01-15', 'Kondisi sangat baik', 'warehouse', 'available'),
(8, 'DS4-001', 'DS401234567', '2023-06-10', 'Kondisi baik', 'warehouse', 'available'),
(8, 'DS4-002', 'DS401234568', '2023-06-10', 'Kondisi baik', 'warehouse', 'available');

-- Insert system settings
INSERT INTO `settings` (`key_name`, `value`, `description`, `type`, `category`) VALUES
('site_name', 'PlayStation Rental Pro', 'Website name', 'string', 'general'),
('site_email', '<EMAIL>', 'Contact email', 'string', 'general'),
('site_phone', '+***********', 'Contact phone', 'string', 'general'),
('rental_hours_start', '08:00', 'Business hours start', 'string', 'business'),
('rental_hours_end', '22:00', 'Business hours end', 'string', 'business'),
('min_rental_duration', '4', 'Minimum rental hours', 'number', 'business'),
('max_rental_duration', '720', 'Maximum rental hours', 'number', 'business'),
('late_fee_per_hour', '10000', 'Late fee per hour', 'number', 'pricing'),
('security_deposit_percentage', '20', 'Security deposit percentage', 'number', 'pricing');

COMMIT;