<!-- <PERSON>er -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Manajemen Produk</h2>
        <p class="text-muted mb-0">Kelola produk rental PlayStation</p>
    </div>
    <a href="<?= base_url('admin/add_product') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Tambah Produk
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('admin/products') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Cari Produk</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($search) ?>" placeholder="Nama, brand, model...">
            </div>
            
            <div class="col-md-3">
                <label for="category" class="form-label">Kategori</label>
                <select class="form-select" id="category" name="category">
                    <option value="">Semua Kategori</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category->id ?>" <?= $selected_category == $category->id ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category->name) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Semua Status</option>
                    <option value="active">Aktif</option>
                    <option value="inactive">Tidak Aktif</option>
                    <option value="maintenance">Maintenance</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-gamepad me-2"></i>Daftar Produk
            <span class="badge bg-secondary ms-2"><?= number_format($total_products) ?></span>
        </h5>
        
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportData('excel')">
                <i class="fas fa-file-excel me-1"></i>Excel
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printPage()">
                <i class="fas fa-print me-1"></i>Print
            </button>
        </div>
    </div>
    
    <div class="card-body">
        <?php if (empty($products)): ?>
            <div class="text-center py-5">
                <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada produk ditemukan</h5>
                <p class="text-muted">Silakan tambah produk baru atau ubah filter pencarian</p>
                <a href="<?= base_url('admin/add_product') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Tambah Produk Pertama
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">Gambar</th>
                            <th width="25%">Produk</th>
                            <th width="15%">Kategori</th>
                            <th width="15%">Harga/Hari</th>
                            <th width="10%">Status</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = ($current_page - 1) * 10 + 1;
                        foreach ($products as $product): 
                        ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td>
                                    <?php if ($product->images): ?>
                                        <?php $images = json_decode($product->images, true); ?>
                                        <img src="<?= base_url('uploads/products/' . $images[0]) ?>" 
                                             alt="<?= htmlspecialchars($product->name) ?>" 
                                             class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 60px; height: 60px; border-radius: 5px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1"><?= htmlspecialchars($product->name) ?></h6>
                                        <small class="text-muted">
                                            <?= $product->brand ? htmlspecialchars($product->brand) : '' ?>
                                            <?= $product->model ? ' - ' . htmlspecialchars($product->model) : '' ?>
                                        </small>
                                        <br>
                                        <span class="badge bg-info"><?= ucfirst($product->condition_status) ?></span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($product->category_name) ?></span>
                                </td>
                                <td>
                                    <strong>Rp <?= number_format($product->rental_price_daily) ?></strong>
                                    <?php if ($product->rental_price_hourly): ?>
                                        <br><small class="text-muted">Rp <?= number_format($product->rental_price_hourly) ?>/jam</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $status_class = [
                                        'active' => 'success',
                                        'inactive' => 'secondary',
                                        'maintenance' => 'warning'
                                    ];
                                    $status_text = [
                                        'active' => 'Aktif',
                                        'inactive' => 'Tidak Aktif',
                                        'maintenance' => 'Maintenance'
                                    ];
                                    ?>
                                    <span class="badge bg-<?= $status_class[$product->status] ?>">
                                        <?= $status_text[$product->status] ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/edit_product/' . $product->id) ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" data-bs-target="#viewModal<?= $product->id ?>"
                                                title="Lihat Detail">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <a href="<?= base_url('admin/delete_product/' . $product->id) ?>" 
                                           class="btn btn-sm btn-outline-danger" 
                                           onclick="return confirmDelete('Apakah Anda yakin ingin menghapus produk ini?')"
                                           data-bs-toggle="tooltip" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- View Modal -->
                            <div class="modal fade" id="viewModal<?= $product->id ?>" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Detail Produk</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>Informasi Dasar</h6>
                                                    <table class="table table-sm">
                                                        <tr><td>Nama</td><td><?= htmlspecialchars($product->name) ?></td></tr>
                                                        <tr><td>Brand</td><td><?= htmlspecialchars($product->brand) ?></td></tr>
                                                        <tr><td>Model</td><td><?= htmlspecialchars($product->model) ?></td></tr>
                                                        <tr><td>Kondisi</td><td><?= ucfirst($product->condition_status) ?></td></tr>
                                                        <tr><td>Status</td><td><?= $status_text[$product->status] ?></td></tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Harga Rental</h6>
                                                    <table class="table table-sm">
                                                        <?php if ($product->rental_price_hourly): ?>
                                                        <tr><td>Per Jam</td><td>Rp <?= number_format($product->rental_price_hourly) ?></td></tr>
                                                        <?php endif; ?>
                                                        <tr><td>Per Hari</td><td>Rp <?= number_format($product->rental_price_daily) ?></td></tr>
                                                        <?php if ($product->rental_price_weekly): ?>
                                                        <tr><td>Per Minggu</td><td>Rp <?= number_format($product->rental_price_weekly) ?></td></tr>
                                                        <?php endif; ?>
                                                        <?php if ($product->rental_price_monthly): ?>
                                                        <tr><td>Per Bulan</td><td>Rp <?= number_format($product->rental_price_monthly) ?></td></tr>
                                                        <?php endif; ?>
                                                        <tr><td>Deposit</td><td>Rp <?= number_format($product->security_deposit) ?></td></tr>
                                                    </table>
                                                </div>
                                            </div>
                                            
                                            <?php if ($product->description): ?>
                                            <div class="mt-3">
                                                <h6>Deskripsi</h6>
                                                <p><?= nl2br(htmlspecialchars($product->description)) ?></p>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($product->specifications): ?>
                                            <div class="mt-3">
                                                <h6>Spesifikasi</h6>
                                                <?php $specs = json_decode($product->specifications, true); ?>
                                                <div class="row">
                                                    <?php foreach ($specs as $key => $value): ?>
                                                    <div class="col-md-6">
                                                        <strong><?= ucfirst(str_replace('_', ' ', $key)) ?>:</strong> <?= htmlspecialchars($value) ?>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Product pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($current_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= base_url('admin/products?page=' . ($current_page - 1) . '&search=' . urlencode($search) . '&category=' . $selected_category) ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                            <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                                <a class="page-link" href="<?= base_url('admin/products?page=' . $i . '&search=' . urlencode($search) . '&category=' . $selected_category) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($current_page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= base_url('admin/products?page=' . ($current_page + 1) . '&search=' . urlencode($search) . '&category=' . $selected_category) ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
// Real-time search
let searchTimeout;
$('#search').on('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(function() {
        $('#search').closest('form').submit();
    }, 500);
});

// Export functions
function exportData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open('<?= base_url('admin/products/export') ?>?' + params.toString(), '_blank');
}
</script>
