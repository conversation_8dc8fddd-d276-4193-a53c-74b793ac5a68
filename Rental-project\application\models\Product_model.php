<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Get all products with pagination
     */
    public function get_products($limit = 10, $offset = 0, $search = '', $category_id = '', $status = 'active') {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'c.id = p.category_id', 'left');
        
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('p.name', $search);
            $this->db->or_like('p.description', $search);
            $this->db->or_like('p.brand', $search);
            $this->db->or_like('p.model', $search);
            $this->db->group_end();
        }
        
        if (!empty($category_id)) {
            $this->db->where('p.category_id', $category_id);
        }
        
        if (!empty($status)) {
            $this->db->where('p.status', $status);
        }
        
        $this->db->order_by('p.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * Count products
     */
    public function count_products($search = '', $category_id = '', $status = 'active') {
        $this->db->from('products p');
        
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('p.name', $search);
            $this->db->or_like('p.description', $search);
            $this->db->or_like('p.brand', $search);
            $this->db->or_like('p.model', $search);
            $this->db->group_end();
        }
        
        if (!empty($category_id)) {
            $this->db->where('p.category_id', $category_id);
        }
        
        if (!empty($status)) {
            $this->db->where('p.status', $status);
        }
        
        return $this->db->count_all_results();
    }

    /**
     * Get product by ID
     */
    public function get_by_id($id) {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'c.id = p.category_id', 'left');
        $this->db->where('p.id', $id);
        
        return $this->db->get()->row();
    }

    /**
     * Get product by slug
     */
    public function get_by_slug($slug) {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'c.id = p.category_id', 'left');
        $this->db->where('p.slug', $slug);
        
        return $this->db->get()->row();
    }

    /**
     * Create new product
     */
    public function create_product($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('products', $data);
    }

    /**
     * Update product
     */
    public function update_product($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->where('id', $id)->update('products', $data);
    }

    /**
     * Delete product
     */
    public function delete_product($id) {
        return $this->db->where('id', $id)->delete('products');
    }

    /**
     * Check if slug exists
     */
    public function slug_exists($slug, $exclude_id = null) {
        $this->db->where('slug', $slug);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get('products')->num_rows() > 0;
    }

    /**
     * Generate unique slug
     */
    public function generate_slug($name, $exclude_id = null) {
        $slug = url_title($name, 'dash', TRUE);
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slug_exists($slug, $exclude_id)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Get featured products
     */
    public function get_featured_products($limit = 6) {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'c.id = p.category_id', 'left');
        $this->db->where('p.status', 'active');
        $this->db->order_by('p.created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get products by category
     */
    public function get_by_category($category_id, $limit = 10, $offset = 0) {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'c.id = p.category_id', 'left');
        $this->db->where('p.category_id', $category_id);
        $this->db->where('p.status', 'active');
        $this->db->order_by('p.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        return $this->db->get()->result();
    }

    /**
     * Get available inventory for product
     */
    public function get_available_inventory($product_id, $start_date = null, $end_date = null) {
        $this->db->select('i.*');
        $this->db->from('inventory i');
        $this->db->where('i.product_id', $product_id);
        $this->db->where('i.status', 'available');
        
        // Check for conflicting rentals if dates provided
        if ($start_date && $end_date) {
            $this->db->where('i.id NOT IN (
                SELECT ri.inventory_id 
                FROM rental_items ri 
                JOIN rentals r ON r.id = ri.rental_id 
                WHERE ri.inventory_id IS NOT NULL 
                AND r.rental_status IN ("confirmed", "active") 
                AND (
                    (r.start_date <= "' . $end_date . '" AND r.end_date >= "' . $start_date . '")
                )
            )');
        }
        
        return $this->db->get()->result();
    }

    /**
     * Check product availability
     */
    public function check_availability($product_id, $quantity, $start_date, $end_date) {
        $available_inventory = $this->get_available_inventory($product_id, $start_date, $end_date);
        return count($available_inventory) >= $quantity;
    }

    /**
     * Get product statistics
     */
    public function get_product_stats() {
        $stats = [];
        
        // Total products
        $stats['total_products'] = $this->db->where('status', 'active')->count_all_results('products');
        
        // Products by category
        $this->db->select('c.name, COUNT(p.id) as count');
        $this->db->from('categories c');
        $this->db->join('products p', 'p.category_id = c.id AND p.status = "active"', 'left');
        $this->db->group_by('c.id');
        $stats['by_category'] = $this->db->get()->result();
        
        // Most rented products
        $this->db->select('p.name, COUNT(ri.id) as rental_count');
        $this->db->from('products p');
        $this->db->join('rental_items ri', 'ri.product_id = p.id', 'left');
        $this->db->join('rentals r', 'r.id = ri.rental_id AND r.rental_status != "cancelled"', 'left');
        $this->db->where('p.status', 'active');
        $this->db->group_by('p.id');
        $this->db->order_by('rental_count', 'DESC');
        $this->db->limit(5);
        $stats['most_rented'] = $this->db->get()->result();
        
        return $stats;
    }

    /**
     * Get low stock products
     */
    public function get_low_stock_products($threshold = 2) {
        $this->db->select('p.*, COUNT(i.id) as available_stock');
        $this->db->from('products p');
        $this->db->join('inventory i', 'i.product_id = p.id AND i.status = "available"', 'left');
        $this->db->where('p.status', 'active');
        $this->db->group_by('p.id');
        $this->db->having('available_stock <=', $threshold);
        $this->db->order_by('available_stock', 'ASC');
        
        return $this->db->get()->result();
    }

    /**
     * Search products
     */
    public function search_products($query, $limit = 10) {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'c.id = p.category_id', 'left');
        $this->db->where('p.status', 'active');
        
        $this->db->group_start();
        $this->db->like('p.name', $query);
        $this->db->or_like('p.description', $query);
        $this->db->or_like('p.brand', $query);
        $this->db->or_like('p.model', $query);
        $this->db->or_like('c.name', $query);
        $this->db->group_end();
        
        $this->db->order_by('p.name', 'ASC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get product price for duration
     */
    public function calculate_price($product_id, $duration_hours) {
        $product = $this->get_by_id($product_id);
        if (!$product) return 0;
        
        $price = 0;
        
        if ($duration_hours <= 24 && $product->rental_price_hourly) {
            // Hourly rate
            $price = $product->rental_price_hourly * $duration_hours;
        } elseif ($duration_hours <= 168 && $product->rental_price_daily) {
            // Daily rate (up to 1 week)
            $days = ceil($duration_hours / 24);
            $price = $product->rental_price_daily * $days;
        } elseif ($duration_hours <= 720 && $product->rental_price_weekly) {
            // Weekly rate (up to 1 month)
            $weeks = ceil($duration_hours / 168);
            $price = $product->rental_price_weekly * $weeks;
        } elseif ($product->rental_price_monthly) {
            // Monthly rate
            $months = ceil($duration_hours / 720);
            $price = $product->rental_price_monthly * $months;
        } else {
            // Fallback to daily rate
            $days = ceil($duration_hours / 24);
            $price = $product->rental_price_daily * $days;
        }
        
        return $price;
    }
}
