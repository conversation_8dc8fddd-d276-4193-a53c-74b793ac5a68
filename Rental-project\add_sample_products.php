<?php
// Add sample products to database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'rental';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Insert sample products
    $products = [
        [
            'category_id' => 1,
            'name' => 'PlayStation 5 Standard Edition',
            'slug' => 'playstation-5-standard-edition',
            'description' => 'Konsol gaming terbaru dari Sony dengan performa ultra-high speed SSD dan ray tracing',
            'brand' => 'Sony',
            'model' => 'CFI-1216A',
            'condition_status' => 'excellent',
            'rental_price_hourly' => 25000,
            'rental_price_daily' => 150000,
            'rental_price_weekly' => 900000,
            'rental_price_monthly' => 3000000,
            'security_deposit' => 500000,
            'replacement_cost' => 8000000,
            'specifications' => json_encode([
                'storage' => '825GB SSD',
                'cpu' => 'AMD Zen 2',
                'gpu' => 'AMD RDNA 2',
                'ram' => '16GB GDDR6'
            ]),
            'features' => json_encode([
                'Ray Tracing',
                '3D Audio',
                'Haptic Feedback',
                'Adaptive Triggers'
            ]),
            'included_accessories' => json_encode([
                'DualSense Controller',
                'HDMI Cable',
                'Power Cable',
                'USB Cable'
            ]),
            'status' => 'active'
        ],
        [
            'category_id' => 1,
            'name' => 'PlayStation 4 Pro',
            'slug' => 'playstation-4-pro',
            'description' => 'PlayStation 4 dengan performa enhanced untuk gaming 4K',
            'brand' => 'Sony',
            'model' => 'CUH-7216B',
            'condition_status' => 'excellent',
            'rental_price_hourly' => 20000,
            'rental_price_daily' => 100000,
            'rental_price_weekly' => 600000,
            'rental_price_monthly' => 2000000,
            'security_deposit' => 300000,
            'replacement_cost' => 5000000,
            'specifications' => json_encode([
                'storage' => '1TB HDD',
                'cpu' => 'AMD Jaguar',
                'gpu' => 'AMD Radeon',
                'ram' => '8GB GDDR5'
            ]),
            'features' => json_encode([
                '4K Gaming',
                'HDR Support',
                'Boost Mode'
            ]),
            'included_accessories' => json_encode([
                'DualShock 4 Controller',
                'HDMI Cable',
                'Power Cable',
                'USB Cable'
            ]),
            'status' => 'active'
        ],
        [
            'category_id' => 3,
            'name' => 'Smart TV 55 inch 4K',
            'slug' => 'smart-tv-55-inch-4k',
            'description' => 'Smart TV 55 inch dengan resolusi 4K untuk pengalaman gaming terbaik',
            'brand' => 'Samsung',
            'model' => 'UA55AU7700',
            'condition_status' => 'excellent',
            'rental_price_hourly' => 15000,
            'rental_price_daily' => 75000,
            'rental_price_weekly' => 450000,
            'rental_price_monthly' => 1500000,
            'security_deposit' => 200000,
            'replacement_cost' => 8000000,
            'specifications' => json_encode([
                'size' => '55 inch',
                'resolution' => '4K UHD',
                'refresh_rate' => '60Hz',
                'smart_tv' => 'Yes'
            ]),
            'features' => json_encode([
                '4K UHD',
                'Smart TV',
                'HDR10+',
                'Game Mode'
            ]),
            'included_accessories' => json_encode([
                'Remote Control',
                'Power Cable',
                'HDMI Cable'
            ]),
            'status' => 'active'
        ],
        [
            'category_id' => 2,
            'name' => 'DualSense Controller PS5',
            'slug' => 'dualsense-controller-ps5',
            'description' => 'Controller wireless untuk PlayStation 5 dengan haptic feedback',
            'brand' => 'Sony',
            'model' => 'CFI-ZCT1W',
            'condition_status' => 'excellent',
            'rental_price_hourly' => 5000,
            'rental_price_daily' => 25000,
            'rental_price_weekly' => 150000,
            'rental_price_monthly' => 500000,
            'security_deposit' => 50000,
            'replacement_cost' => 800000,
            'specifications' => json_encode([
                'connectivity' => 'Wireless',
                'battery' => 'Built-in',
                'features' => 'Haptic Feedback'
            ]),
            'features' => json_encode([
                'Haptic Feedback',
                'Adaptive Triggers',
                'Built-in Microphone',
                'Motion Sensors'
            ]),
            'included_accessories' => json_encode([
                'USB-C Cable'
            ]),
            'status' => 'active'
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO products (
            category_id, name, slug, description, brand, model, condition_status,
            rental_price_hourly, rental_price_daily, rental_price_weekly, rental_price_monthly,
            security_deposit, replacement_cost, specifications, features, included_accessories, status,
            created_at, updated_at
        ) VALUES (
            :category_id, :name, :slug, :description, :brand, :model, :condition_status,
            :rental_price_hourly, :rental_price_daily, :rental_price_weekly, :rental_price_monthly,
            :security_deposit, :replacement_cost, :specifications, :features, :included_accessories, :status,
            NOW(), NOW()
        )
    ");
    
    foreach ($products as $product) {
        $stmt->execute($product);
        echo "Added product: " . $product['name'] . "\n";
    }
    
    echo "\nSample products added successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
