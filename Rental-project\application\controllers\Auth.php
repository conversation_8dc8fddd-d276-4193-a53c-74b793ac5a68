<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('User_model');
        $this->load->library('form_validation');
        $this->load->helper(['url', 'form']);
    }

    /**
     * Login page
     */
    public function login() {
        // Redirect if already logged in
        if ($this->session->userdata('user_id')) {
            $role = $this->session->userdata('role');
            redirect($role === 'admin' ? 'admin/dashboard' : 'customer/dashboard');
        }

        $data['title'] = 'Login - PlayStation Rental';
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('email_username', 'Email/Username', 'required|trim');
            $this->form_validation->set_rules('password', 'Password', 'required');
            
            if ($this->form_validation->run()) {
                $email_username = $this->input->post('email_username');
                $password = $this->input->post('password');
                $remember = $this->input->post('remember');
                
                $user = $this->User_model->verify_login($email_username, $password);
                
                if ($user) {
                    // Set session data
                    $session_data = [
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'full_name' => $user->full_name,
                        'role' => $user->role,
                        'logged_in' => TRUE
                    ];
                    $this->session->set_userdata($session_data);
                    
                    // Create session token if remember me
                    if ($remember) {
                        $token = $this->User_model->create_session(
                            $user->id,
                            $this->input->ip_address(),
                            $this->input->user_agent()
                        );
                        
                        // Set cookie for 30 days
                        $this->input->set_cookie([
                            'name' => 'remember_token',
                            'value' => $token,
                            'expire' => 30 * 24 * 60 * 60,
                            'secure' => TRUE,
                            'httponly' => TRUE
                        ]);
                    }
                    
                    // Update last login
                    $this->User_model->update_last_login($user->id);
                    
                    // Redirect based on role
                    $redirect_url = $user->role === 'admin' ? 'admin/dashboard' : 'customer/dashboard';
                    
                    if ($this->input->is_ajax_request()) {
                        echo json_encode(['success' => true, 'redirect' => base_url($redirect_url)]);
                        return;
                    }
                    
                    $this->session->set_flashdata('success', 'Login berhasil! Selamat datang, ' . $user->full_name);
                    redirect($redirect_url);
                } else {
                    $error = 'Email/Username atau password salah';
                    
                    if ($this->input->is_ajax_request()) {
                        echo json_encode(['success' => false, 'message' => $error]);
                        return;
                    }
                    
                    $this->session->set_flashdata('error', $error);
                }
            } else {
                if ($this->input->is_ajax_request()) {
                    echo json_encode(['success' => false, 'message' => validation_errors()]);
                    return;
                }
            }
        }
        
        $this->load->view('auth/login', $data);
    }

    /**
     * Register page
     */
    public function register() {
        // Redirect if already logged in
        if ($this->session->userdata('user_id')) {
            $role = $this->session->userdata('role');
            redirect($role === 'admin' ? 'admin/dashboard' : 'customer/dashboard');
        }

        $data['title'] = 'Register - PlayStation Rental';
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Username', 'required|trim|min_length[3]|max_length[50]|alpha_numeric');
            $this->form_validation->set_rules('email', 'Email', 'required|trim|valid_email|max_length[100]');
            $this->form_validation->set_rules('password', 'Password', 'required|min_length[6]');
            $this->form_validation->set_rules('password_confirm', 'Konfirmasi Password', 'required|matches[password]');
            $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('phone', 'Nomor Telepon', 'trim|max_length[20]');
            
            if ($this->form_validation->run()) {
                $username = $this->input->post('username');
                $email = $this->input->post('email');
                
                // Check if email or username already exists
                if ($this->User_model->email_exists($email)) {
                    $error = 'Email sudah terdaftar';
                } elseif ($this->User_model->username_exists($username)) {
                    $error = 'Username sudah digunakan';
                } else {
                    $user_data = [
                        'username' => $username,
                        'email' => $email,
                        'password' => $this->input->post('password'),
                        'full_name' => $this->input->post('full_name'),
                        'phone' => $this->input->post('phone'),
                        'role' => 'customer',
                        'status' => 'active'
                    ];
                    
                    if ($this->User_model->create_user($user_data)) {
                        if ($this->input->is_ajax_request()) {
                            echo json_encode(['success' => true, 'message' => 'Registrasi berhasil! Silakan login.']);
                            return;
                        }
                        
                        $this->session->set_flashdata('success', 'Registrasi berhasil! Silakan login.');
                        redirect('auth/login');
                    } else {
                        $error = 'Terjadi kesalahan saat registrasi';
                    }
                }
                
                if (isset($error)) {
                    if ($this->input->is_ajax_request()) {
                        echo json_encode(['success' => false, 'message' => $error]);
                        return;
                    }
                    
                    $this->session->set_flashdata('error', $error);
                }
            } else {
                if ($this->input->is_ajax_request()) {
                    echo json_encode(['success' => false, 'message' => validation_errors()]);
                    return;
                }
            }
        }
        
        $this->load->view('auth/register', $data);
    }

    /**
     * Logout
     */
    public function logout() {
        // Delete remember token if exists
        $remember_token = $this->input->cookie('remember_token');
        if ($remember_token) {
            $this->User_model->delete_session($remember_token);
            delete_cookie('remember_token');
        }
        
        // Destroy session
        $this->session->sess_destroy();
        
        $this->session->set_flashdata('success', 'Anda telah logout');
        redirect('auth/login');
    }

    /**
     * Forgot password
     */
    public function forgot_password() {
        $data['title'] = 'Lupa Password - PlayStation Rental';
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('email', 'Email', 'required|trim|valid_email');
            
            if ($this->form_validation->run()) {
                $email = $this->input->post('email');
                $user = $this->User_model->get_by_email($email);
                
                if ($user) {
                    $token = $this->User_model->create_reset_token($email);
                    
                    // TODO: Send email with reset link
                    // For now, just show success message
                    
                    if ($this->input->is_ajax_request()) {
                        echo json_encode(['success' => true, 'message' => 'Link reset password telah dikirim ke email Anda.']);
                        return;
                    }
                    
                    $this->session->set_flashdata('success', 'Link reset password telah dikirim ke email Anda.');
                } else {
                    $error = 'Email tidak ditemukan';
                    
                    if ($this->input->is_ajax_request()) {
                        echo json_encode(['success' => false, 'message' => $error]);
                        return;
                    }
                    
                    $this->session->set_flashdata('error', $error);
                }
            } else {
                if ($this->input->is_ajax_request()) {
                    echo json_encode(['success' => false, 'message' => validation_errors()]);
                    return;
                }
            }
        }
        
        $this->load->view('auth/forgot_password', $data);
    }

    /**
     * Reset password
     */
    public function reset_password($token = '') {
        if (empty($token)) {
            show_404();
        }
        
        $reset_data = $this->User_model->verify_reset_token($token);
        if (!$reset_data) {
            $this->session->set_flashdata('error', 'Token reset password tidak valid atau sudah expired');
            redirect('auth/forgot_password');
        }
        
        $data['title'] = 'Reset Password - PlayStation Rental';
        $data['token'] = $token;
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('password', 'Password Baru', 'required|min_length[6]');
            $this->form_validation->set_rules('password_confirm', 'Konfirmasi Password', 'required|matches[password]');
            
            if ($this->form_validation->run()) {
                $user = $this->User_model->get_by_email($reset_data->email);
                
                if ($user && $this->User_model->update_user($user->id, ['password' => $this->input->post('password')])) {
                    $this->User_model->use_reset_token($token);
                    
                    $this->session->set_flashdata('success', 'Password berhasil direset. Silakan login dengan password baru.');
                    redirect('auth/login');
                } else {
                    $this->session->set_flashdata('error', 'Terjadi kesalahan saat reset password');
                }
            }
        }
        
        $this->load->view('auth/reset_password', $data);
    }

    /**
     * Check authentication via AJAX
     */
    public function check_auth() {
        $authenticated = $this->session->userdata('user_id') ? true : false;
        echo json_encode(['authenticated' => $authenticated]);
    }
}
