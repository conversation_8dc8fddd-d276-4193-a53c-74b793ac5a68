<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Customer extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->load->model(['User_model', 'Product_model', 'Category_model']);
        $this->load->helper(['url', 'form']);
        
        // Require customer access
        $this->auth_lib->require_customer();
    }

    /**
     * Customer Dashboard
     */
    public function index() {
        redirect('customer/dashboard');
    }

    /**
     * Dashboard
     */
    public function dashboard() {
        $data['title'] = 'Dashboard Pelanggan - PlayStation Rental Pro';
        $data['user'] = $this->auth_lib->get_user();
        
        // Get customer statistics
        $customer_id = $this->auth_lib->get_user_id();
        $data['stats'] = $this->get_customer_stats($customer_id);
        
        // Get recent rentals (will implement later)
        // $data['recent_rentals'] = $this->get_recent_rentals($customer_id);
        
        $this->load->view('customer/layout/header', $data);
        $this->load->view('customer/dashboard', $data);
        $this->load->view('customer/layout/footer');
    }

    /**
     * Browse Products
     */
    public function products() {
        $data['title'] = 'Produk Rental - PlayStation Rental Pro';
        
        // Pagination
        $limit = 12;
        $offset = ($this->input->get('page') ?? 1 - 1) * $limit;
        $search = $this->input->get('search') ?? '';
        $category_id = $this->input->get('category') ?? '';
        $sort = $this->input->get('sort') ?? 'newest';
        
        // Get products
        $data['products'] = $this->Product_model->get_products($limit, $offset, $search, $category_id, 'active');
        $data['total_products'] = $this->Product_model->count_products($search, $category_id, 'active');
        
        // Get categories for filter
        $data['categories'] = $this->Category_model->get_all('active');
        
        // Pagination data
        $data['current_page'] = $this->input->get('page') ?? 1;
        $data['total_pages'] = ceil($data['total_products'] / $limit);
        $data['search'] = $search;
        $data['selected_category'] = $category_id;
        $data['sort'] = $sort;
        
        $this->load->view('customer/layout/header', $data);
        $this->load->view('customer/products/index', $data);
        $this->load->view('customer/layout/footer');
    }

    /**
     * Product Detail
     */
    public function product_detail($slug) {
        $data['product'] = $this->Product_model->get_by_slug($slug);
        
        if (!$data['product'] || $data['product']->status !== 'active') {
            show_404();
        }
        
        $data['title'] = $data['product']->name . ' - PlayStation Rental Pro';
        
        // Get related products
        $data['related_products'] = $this->Product_model->get_by_category($data['product']->category_id, 4);
        
        // Remove current product from related products
        $data['related_products'] = array_filter($data['related_products'], function($product) use ($data) {
            return $product->id !== $data['product']->id;
        });
        
        $this->load->view('customer/layout/header', $data);
        $this->load->view('customer/products/detail', $data);
        $this->load->view('customer/layout/footer');
    }

    /**
     * My Rentals
     */
    public function rentals() {
        $data['title'] = 'Rental Saya - PlayStation Rental Pro';
        
        // Get customer rentals (will implement later when rental system is ready)
        // $data['rentals'] = $this->Rental_model->get_customer_rentals($this->auth_lib->get_user_id());
        $data['rentals'] = []; // Temporary empty array
        
        $this->load->view('customer/layout/header', $data);
        $this->load->view('customer/rentals/index', $data);
        $this->load->view('customer/layout/footer');
    }

    /**
     * Profile Management
     */
    public function profile() {
        $data['title'] = 'Profil Saya - PlayStation Rental Pro';
        $data['user'] = $this->auth_lib->get_user();
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            
            $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('phone', 'Nomor Telepon', 'trim|max_length[20]');
            $this->form_validation->set_rules('address', 'Alamat', 'trim|max_length[500]');
            
            if ($this->form_validation->run()) {
                $user_data = [
                    'full_name' => $this->input->post('full_name'),
                    'phone' => $this->input->post('phone'),
                    'address' => $this->input->post('address')
                ];
                
                if ($this->User_model->update_user($this->auth_lib->get_user_id(), $user_data)) {
                    // Update session data
                    $this->session->set_userdata('full_name', $user_data['full_name']);
                    
                    $this->session->set_flashdata('success', 'Profil berhasil diupdate');
                    redirect('customer/profile');
                } else {
                    $this->session->set_flashdata('error', 'Gagal mengupdate profil');
                }
            }
        }
        
        $this->load->view('customer/layout/header', $data);
        $this->load->view('customer/profile', $data);
        $this->load->view('customer/layout/footer');
    }

    /**
     * Change Password
     */
    public function change_password() {
        $data['title'] = 'Ubah Password - PlayStation Rental Pro';
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            
            $this->form_validation->set_rules('current_password', 'Password Saat Ini', 'required');
            $this->form_validation->set_rules('new_password', 'Password Baru', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[new_password]');
            
            if ($this->form_validation->run()) {
                $user = $this->auth_lib->get_user();
                
                if (password_verify($this->input->post('current_password'), $user->password)) {
                    $user_data = [
                        'password' => $this->input->post('new_password')
                    ];
                    
                    if ($this->User_model->update_user($user->id, $user_data)) {
                        $this->session->set_flashdata('success', 'Password berhasil diubah');
                        redirect('customer/profile');
                    } else {
                        $this->session->set_flashdata('error', 'Gagal mengubah password');
                    }
                } else {
                    $this->session->set_flashdata('error', 'Password saat ini salah');
                }
            }
        }
        
        $this->load->view('customer/layout/header', $data);
        $this->load->view('customer/change_password', $data);
        $this->load->view('customer/layout/footer');
    }

    /**
     * Get customer statistics
     */
    private function get_customer_stats($customer_id) {
        $stats = [];
        
        // Total rentals (will implement later)
        $stats['total_rentals'] = 0;
        
        // Active rentals (will implement later)
        $stats['active_rentals'] = 0;
        
        // Completed rentals (will implement later)
        $stats['completed_rentals'] = 0;
        
        // Total spent (will implement later)
        $stats['total_spent'] = 0;
        
        return $stats;
    }

    /**
     * Search products (AJAX)
     */
    public function search() {
        $query = $this->input->get('q');
        
        if (strlen($query) >= 2) {
            $products = $this->Product_model->search_products($query, 10);
            
            $results = [];
            foreach ($products as $product) {
                $results[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'brand' => $product->brand,
                    'category' => $product->category_name,
                    'price' => $product->rental_price_daily,
                    'url' => base_url('customer/product/' . $product->slug)
                ];
            }
            
            echo json_encode($results);
        } else {
            echo json_encode([]);
        }
    }

    /**
     * Add to cart (will implement later)
     */
    public function add_to_cart() {
        // Will implement when cart system is ready
        echo json_encode(['success' => false, 'message' => 'Cart system belum tersedia']);
    }

    /**
     * Check availability (AJAX)
     */
    public function check_availability() {
        $product_id = $this->input->post('product_id');
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');
        $quantity = $this->input->post('quantity') ?: 1;
        
        if ($product_id && $start_date && $end_date) {
            $available = $this->Product_model->check_availability($product_id, $quantity, $start_date, $end_date);
            
            echo json_encode([
                'available' => $available,
                'message' => $available ? 'Produk tersedia' : 'Produk tidak tersedia untuk tanggal tersebut'
            ]);
        } else {
            echo json_encode(['available' => false, 'message' => 'Data tidak lengkap']);
        }
    }
}
