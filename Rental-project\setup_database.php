<?php

/**
 * Database Setup Script for PlayStation Rental Pro
 * Run this script to create database and import initial data
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'rental';

echo "=== PlayStation Rental Pro - Database Setup ===\n\n";

try {
    // Connect to MySQL without selecting database
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "✓ Connected to MySQL server\n";

    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Database '$database' created/verified\n";

    // Select the database
    $pdo->exec("USE `$database`");
    echo "✓ Using database '$database'\n\n";

    // Read SQL file
    $sqlFile = __DIR__ . '/rental.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }

    $sql = file_get_contents($sqlFile);
    echo "✓ SQL file loaded\n";

    // Remove comments and split SQL into statements
    $sql = preg_replace('/--.*$/m', '', $sql); // Remove single line comments
    $sql = preg_replace('/\/\*.*?\*\//s', '', $sql); // Remove multi-line comments

    // Split by semicolon but be careful with semicolons inside strings
    $statements = [];
    $current = '';
    $inString = false;
    $stringChar = '';

    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];

        if (!$inString && ($char === '"' || $char === "'")) {
            $inString = true;
            $stringChar = $char;
        } elseif ($inString && $char === $stringChar) {
            $inString = false;
        } elseif (!$inString && $char === ';') {
            $stmt = trim($current);
            if (!empty($stmt)) {
                $statements[] = $stmt;
            }
            $current = '';
            continue;
        }

        $current .= $char;
    }

    // Add the last statement if any
    $stmt = trim($current);
    if (!empty($stmt)) {
        $statements[] = $stmt;
    }

    echo "✓ Found " . count($statements) . " SQL statements\n\n";

    // Execute each statement
    $executed = 0;
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^\s*(SET|START|COMMIT)/i', $statement)) {
            try {
                $pdo->exec($statement);
                $executed++;

                // Show progress for major operations
                if (preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "  → Created table: {$matches[1]}\n";
                } elseif (preg_match('/INSERT INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "  → Inserted data into: {$matches[1]}\n";
                }
            } catch (PDOException $e) {
                // Skip if table already exists
                if (
                    strpos($e->getMessage(), 'already exists') === false &&
                    strpos($e->getMessage(), 'Duplicate entry') === false
                ) {
                    echo "  ⚠ Warning: " . $e->getMessage() . "\n";
                    echo "  Statement: " . substr($statement, 0, 100) . "...\n";
                }
            }
        }
    }

    echo "\n✓ Executed $executed SQL statements\n\n";

    // Verify tables created
    $result = $pdo->query("SHOW TABLES");
    $tables = $result->fetchAll(PDO::FETCH_COLUMN);

    echo "✓ Database setup completed!\n";
    echo "✓ Created " . count($tables) . " tables:\n";

    foreach ($tables as $table) {
        echo "  - $table\n";
    }

    echo "\n=== Setup Summary ===\n";
    echo "Database: $database\n";
    echo "Tables: " . count($tables) . "\n";
    echo "Status: Ready to use!\n\n";

    echo "=== Default Login Credentials ===\n";
    echo "Admin Login:\n";
    echo "  Email: <EMAIL>\n";
    echo "  Password: password\n\n";
    echo "Customer Login:\n";
    echo "  Email: <EMAIL>\n";
    echo "  Password: password\n\n";

    echo "You can now access the website at: http://localhost:8000\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
