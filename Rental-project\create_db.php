<?php
// Simple database creation script
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'rental';

try {
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database '$database' created successfully!\n";
    
    // Use database
    $pdo->exec("USE `$database`");
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL UNIQUE,
          `email` varchar(100) NOT NULL UNIQUE,
          `password` varchar(255) NOT NULL,
          `full_name` varchar(100) NOT NULL,
          `phone` varchar(20) DEFAULT NULL,
          `address` text DEFAULT NULL,
          `role` enum('admin','customer','staff') NOT NULL DEFAULT 'customer',
          `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
          `email_verified` tinyint(1) DEFAULT 0,
          `profile_image` varchar(255) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_email` (`email`),
          KEY `idx_role` (`role`),
          KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "Users table created!\n";
    
    // Create categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `categories` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `slug` varchar(100) NOT NULL UNIQUE,
          `description` text DEFAULT NULL,
          `image` varchar(255) DEFAULT NULL,
          `sort_order` int(11) DEFAULT 0,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_slug` (`slug`),
          KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "Categories table created!\n";
    
    // Create products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `products` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `category_id` int(11) NOT NULL,
          `name` varchar(200) NOT NULL,
          `slug` varchar(200) NOT NULL UNIQUE,
          `description` text DEFAULT NULL,
          `specifications` json DEFAULT NULL,
          `brand` varchar(100) DEFAULT NULL,
          `model` varchar(100) DEFAULT NULL,
          `condition_status` enum('new','excellent','good','fair') NOT NULL DEFAULT 'excellent',
          `rental_price_hourly` decimal(10,2) DEFAULT NULL,
          `rental_price_daily` decimal(10,2) NOT NULL,
          `rental_price_weekly` decimal(10,2) DEFAULT NULL,
          `rental_price_monthly` decimal(10,2) DEFAULT NULL,
          `security_deposit` decimal(10,2) NOT NULL DEFAULT 0,
          `replacement_cost` decimal(10,2) DEFAULT NULL,
          `images` json DEFAULT NULL,
          `features` json DEFAULT NULL,
          `included_accessories` json DEFAULT NULL,
          `status` enum('active','inactive','maintenance') NOT NULL DEFAULT 'active',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_category` (`category_id`),
          KEY `idx_slug` (`slug`),
          KEY `idx_status` (`status`),
          KEY `idx_brand` (`brand`),
          FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "Products table created!\n";
    
    // Insert admin user (password: password)
    $pdo->exec("
        INSERT IGNORE INTO `users` (`username`, `email`, `password`, `full_name`, `role`, `status`, `email_verified`) VALUES
        ('admin', '<EMAIL>', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', 'active', 1)
    ");
    echo "Admin user created!\n";
    
    // Insert sample customers
    $pdo->exec("
        INSERT IGNORE INTO `users` (`username`, `email`, `password`, `full_name`, `phone`, `role`, `status`, `email_verified`) VALUES
        ('johndoe', '<EMAIL>', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', '08123456789', 'customer', 'active', 1),
        ('janesmith', '<EMAIL>', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane Smith', '08987654321', 'customer', 'active', 1)
    ");
    echo "Sample customers created!\n";
    
    // Insert categories
    $pdo->exec("
        INSERT IGNORE INTO `categories` (`name`, `slug`, `description`, `status`) VALUES
        ('PlayStation Consoles', 'playstation-consoles', 'Gaming consoles PS3, PS4, PS5', 'active'),
        ('Controllers', 'controllers', 'Gaming controllers and accessories', 'active'),
        ('TVs & Monitors', 'tvs-monitors', 'Display devices for gaming', 'active'),
        ('Accessories', 'accessories', 'Gaming accessories and peripherals', 'active')
    ");
    echo "Categories created!\n";
    
    echo "\n=== Setup Complete! ===\n";
    echo "Database: $database\n";
    echo "Admin Login: <EMAIL> / password\n";
    echo "Customer Login: <EMAIL> / password\n";
    echo "Website: http://localhost:8000\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
