<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->load->model(['User_model', 'Product_model']);
        $this->load->helper(['url', 'form']);
        
        // Check remember token for auto-login
        $this->auth_lib->check_remember_token();
    }

    /**
     * Landing page
     */
    public function index() {
        // Redirect if already logged in
        if ($this->auth_lib->is_logged_in()) {
            $role = $this->session->userdata('role');
            redirect($role === 'admin' ? 'admin/dashboard' : 'customer/dashboard');
        }

        $data['title'] = 'PlayStation Rental Pro - Sewa PlayStation Terpercaya';
        
        // Get featured products (will implement later)
        // $data['featured_products'] = $this->Product_model->get_featured_products(6);
        
        $this->load->view('home/index', $data);
    }

    /**
     * About page
     */
    public function about() {
        $data['title'] = 'Tentang Kami - PlayStation Rental Pro';
        $this->load->view('home/about', $data);
    }

    /**
     * Services page
     */
    public function services() {
        $data['title'] = 'Layanan Kami - PlayStation Rental Pro';
        $this->load->view('home/services', $data);
    }

    /**
     * Contact page
     */
    public function contact() {
        $data['title'] = 'Kontak - PlayStation Rental Pro';
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            
            $this->form_validation->set_rules('name', 'Nama', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('email', 'Email', 'required|trim|valid_email');
            $this->form_validation->set_rules('subject', 'Subjek', 'required|trim|max_length[200]');
            $this->form_validation->set_rules('message', 'Pesan', 'required|trim|max_length[1000]');
            
            if ($this->form_validation->run()) {
                // TODO: Send email or save to database
                $this->session->set_flashdata('success', 'Pesan Anda telah terkirim. Kami akan segera menghubungi Anda.');
                redirect('home/contact');
            }
        }
        
        $this->load->view('home/contact', $data);
    }

    /**
     * Privacy policy
     */
    public function privacy() {
        $data['title'] = 'Kebijakan Privasi - PlayStation Rental Pro';
        $this->load->view('home/privacy', $data);
    }

    /**
     * Terms of service
     */
    public function terms() {
        $data['title'] = 'Syarat & Ketentuan - PlayStation Rental Pro';
        $this->load->view('home/terms', $data);
    }
}
