<!-- Dashboard Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= number_format($stats['total_customers']) ?></div>
                    <div>Total Pelanggan</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= number_format($stats['total_products']) ?></div>
                    <div>Total Produk</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= number_format($stats['active_rentals']) ?></div>
                    <div>Rental Aktif</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?= number_format($stats['pending_rentals']) ?></div>
                    <div>Rental Pending</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Stats -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Pendapatan Hari Ini</h5>
            </div>
            <div class="card-body text-center">
                <h2 class="text-success mb-0">Rp <?= number_format($stats['daily_revenue']) ?></h2>
                <small class="text-muted">Update terakhir: <?= date('H:i') ?></small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Pendapatan Bulan Ini</h5>
            </div>
            <div class="card-body text-center">
                <h2 class="text-primary mb-0">Rp <?= number_format($stats['monthly_revenue']) ?></h2>
                <small class="text-muted"><?= date('F Y') ?></small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Grafik Pendapatan 7 Hari Terakhir</h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Produk Terpopuler</h5>
            </div>
            <div class="card-body">
                <canvas id="productChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row mb-4">
    <div class="col-lg-6 mb-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Rental Terbaru</h5>
                <a href="<?= base_url('admin/rentals') ?>" class="btn btn-sm btn-outline-light">Lihat Semua</a>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <!-- Sample data - will be replaced with real data -->
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">PlayStation 5 + TV 43"</h6>
                            <p class="mb-1 text-muted">John Doe - 3 hari</p>
                            <small class="text-muted">2 jam yang lalu</small>
                        </div>
                        <span class="badge bg-warning">Pending</span>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">PlayStation 4 Pro</h6>
                            <p class="mb-1 text-muted">Jane Smith - 1 hari</p>
                            <small class="text-muted">4 jam yang lalu</small>
                        </div>
                        <span class="badge bg-success">Aktif</span>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">PlayStation 5 Digital</h6>
                            <p class="mb-1 text-muted">Bob Johnson - 2 hari</p>
                            <small class="text-muted">6 jam yang lalu</small>
                        </div>
                        <span class="badge bg-info">Confirmed</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Peringatan Sistem</h5>
                <a href="<?= base_url('admin/alerts') ?>" class="btn btn-sm btn-outline-light">Lihat Semua</a>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 text-warning">Stok Rendah</h6>
                            <p class="mb-1">PlayStation 5 tersisa 2 unit</p>
                            <small class="text-muted">1 jam yang lalu</small>
                        </div>
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 text-danger">Rental Overdue</h6>
                            <p class="mb-1">3 rental melewati batas waktu</p>
                            <small class="text-muted">2 jam yang lalu</small>
                        </div>
                        <i class="fas fa-clock text-danger"></i>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 text-info">Maintenance Due</h6>
                            <p class="mb-1">5 unit perlu maintenance</p>
                            <small class="text-muted">3 jam yang lalu</small>
                        </div>
                        <i class="fas fa-tools text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Aksi Cepat</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/add_product') ?>" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>Tambah Produk
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/rentals/new') ?>" class="btn btn-success w-100">
                            <i class="fas fa-calendar-plus me-2"></i>Rental Baru
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/customers') ?>" class="btn btn-info w-100">
                            <i class="fas fa-users me-2"></i>Kelola Pelanggan
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/reports') ?>" class="btn btn-warning w-100">
                            <i class="fas fa-chart-bar me-2"></i>Lihat Laporan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['6 hari lalu', '5 hari lalu', '4 hari lalu', '3 hari lalu', '2 hari lalu', 'Kemarin', 'Hari ini'],
            datasets: [{
                label: 'Pendapatan (Rp)',
                data: [1200000, 1500000, 800000, 2100000, 1800000, 2500000, 1900000],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString('id-ID');
                        }
                    }
                }
            }
        }
    });
    
    // Product Chart
    const productCtx = document.getElementById('productChart').getContext('2d');
    new Chart(productCtx, {
        type: 'doughnut',
        data: {
            labels: ['PlayStation 5', 'PlayStation 4', 'PlayStation 3', 'TV & Monitor'],
            datasets: [{
                data: [45, 30, 15, 10],
                backgroundColor: [
                    '#667eea',
                    '#764ba2',
                    '#f093fb',
                    '#28a745'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
