<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Rental Baru</h2>
        <p class="text-muted mb-0">Buat transaksi rental baru untuk pelanggan</p>
    </div>
    <a href="<?= base_url('admin/rentals') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Kembali
    </a>
</div>

<!-- Rental Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Informasi Rental</h5>
            </div>
            <div class="card-body">
                <form id="rentalForm" method="post">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_id" class="form-label">Pelanggan <span class="text-danger">*</span></label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">Pilih Pelanggan</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?= $customer->id ?>" data-phone="<?= $customer->phone ?>" data-email="<?= $customer->email ?>">
                                        <?= htmlspecialchars($customer->full_name) ?> (<?= htmlspecialchars($customer->email) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="rental_date" class="form-label">Tanggal Rental <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="rental_date" name="rental_date" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="start_date" name="start_date" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">Tanggal Selesai <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="end_date" name="end_date" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="delivery_address" class="form-label">Alamat Pengiriman</label>
                        <textarea class="form-control" id="delivery_address" name="delivery_address" rows="3" 
                                  placeholder="Alamat lengkap untuk pengiriman dan pickup"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Catatan</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="Catatan tambahan untuk rental ini"></textarea>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Product Selection -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>Pilih Produk</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="productSearch" placeholder="Cari produk...">
                </div>
                
                <div class="row" id="productList">
                    <?php foreach ($products as $product): ?>
                        <div class="col-md-6 mb-3 product-item" data-name="<?= strtolower($product->name) ?>">
                            <div class="card product-card" style="cursor: pointer;" onclick="selectProduct(<?= $product->id ?>)">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="card-title mb-1"><?= htmlspecialchars($product->name) ?></h6>
                                            <p class="text-muted small mb-2">
                                                <?= htmlspecialchars($product->category_name) ?>
                                                <?php if ($product->brand): ?>
                                                    - <?= htmlspecialchars($product->brand) ?>
                                                <?php endif; ?>
                                            </p>
                                            <div class="price-info">
                                                <strong class="text-primary">Rp <?= number_format($product->rental_price_daily) ?>/hari</strong>
                                                <?php if ($product->rental_price_hourly): ?>
                                                    <br><small class="text-muted">Rp <?= number_format($product->rental_price_hourly) ?>/jam</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <span class="badge bg-success">Tersedia</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Order Summary -->
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 100px;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Ringkasan Rental</h5>
            </div>
            <div class="card-body">
                <div id="selectedProducts">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-gamepad fa-2x mb-2"></i>
                        <p class="mb-0">Belum ada produk dipilih</p>
                        <small>Pilih produk dari daftar di sebelah kiri</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span id="subtotal">Rp 0</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Deposit Keamanan:</span>
                    <span id="deposit">Rp 0</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Biaya Pengiriman:</span>
                    <span id="deliveryFee">Rp 0</span>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between mb-3">
                    <strong>Total:</strong>
                    <strong id="total" class="text-primary">Rp 0</strong>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>Sistem rental akan segera diimplementasikan. Form ini untuk preview interface.</small>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="processRental()" disabled>
                        <i class="fas fa-check me-2"></i>Proses Rental
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                        <i class="fas fa-save me-2"></i>Simpan Draft
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedProducts = [];
let rentalDuration = 0;

// Product search
$('#productSearch').on('input', function() {
    const query = $(this).val().toLowerCase();
    $('.product-item').each(function() {
        const productName = $(this).data('name');
        if (productName.includes(query)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
});

// Select product
function selectProduct(productId) {
    // Find product data
    const productCard = $(`[onclick="selectProduct(${productId})"]`);
    const productName = productCard.find('.card-title').text();
    const productPrice = productCard.find('.text-primary').text();
    
    // Check if already selected
    if (selectedProducts.find(p => p.id === productId)) {
        showToast('Produk sudah dipilih', 'warning');
        return;
    }
    
    // Add to selected products
    selectedProducts.push({
        id: productId,
        name: productName,
        price: productPrice,
        dailyRate: parseInt(productPrice.replace(/[^\d]/g, ''))
    });
    
    updateOrderSummary();
    showToast('Produk berhasil ditambahkan', 'success');
}

// Remove product
function removeProduct(productId) {
    selectedProducts = selectedProducts.filter(p => p.id !== productId);
    updateOrderSummary();
    showToast('Produk dihapus dari rental', 'info');
}

// Update order summary
function updateOrderSummary() {
    const container = $('#selectedProducts');
    
    if (selectedProducts.length === 0) {
        container.html(`
            <div class="text-center text-muted py-4">
                <i class="fas fa-gamepad fa-2x mb-2"></i>
                <p class="mb-0">Belum ada produk dipilih</p>
                <small>Pilih produk dari daftar di sebelah kiri</small>
            </div>
        `);
        $('#subtotal, #deposit, #total').text('Rp 0');
        return;
    }
    
    let html = '';
    let subtotal = 0;
    let deposit = 0;
    
    selectedProducts.forEach(product => {
        const productTotal = product.dailyRate * Math.max(1, rentalDuration);
        subtotal += productTotal;
        deposit += product.dailyRate * 0.2; // 20% deposit
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <small class="fw-bold">${product.name}</small>
                    <br><small class="text-muted">${rentalDuration || 1} hari × ${product.price}</small>
                </div>
                <div class="text-end">
                    <small class="fw-bold">Rp ${formatNumber(productTotal)}</small>
                    <br><button type="button" class="btn btn-sm btn-outline-danger" onclick="removeProduct(${product.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    });
    
    container.html(html);
    
    const deliveryFee = 25000; // Default delivery fee
    const total = subtotal + deposit + deliveryFee;
    
    $('#subtotal').text('Rp ' + formatNumber(subtotal));
    $('#deposit').text('Rp ' + formatNumber(deposit));
    $('#deliveryFee').text('Rp ' + formatNumber(deliveryFee));
    $('#total').text('Rp ' + formatNumber(total));
    
    // Enable process button if products selected
    $('button[onclick="processRental()"]').prop('disabled', selectedProducts.length === 0);
}

// Calculate rental duration when dates change
$('#start_date, #end_date').on('change', function() {
    const startDate = new Date($('#start_date').val());
    const endDate = new Date($('#end_date').val());
    
    if (startDate && endDate && endDate > startDate) {
        rentalDuration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        updateOrderSummary();
    }
});

// Set minimum dates
$(document).ready(function() {
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    
    $('#start_date').attr('min', now.toISOString().slice(0, 16));
    $('#end_date').attr('min', tomorrow.toISOString().slice(0, 16));
    
    // Set default values
    $('#start_date').val(now.toISOString().slice(0, 16));
    $('#end_date').val(tomorrow.toISOString().slice(0, 16));
    
    rentalDuration = 1;
});

// Update end date minimum when start date changes
$('#start_date').on('change', function() {
    const startDate = new Date($(this).val());
    const minEndDate = new Date(startDate.getTime() + 60 * 60 * 1000); // 1 hour minimum
    $('#end_date').attr('min', minEndDate.toISOString().slice(0, 16));
});

// Process rental
function processRental() {
    if (selectedProducts.length === 0) {
        showToast('Pilih minimal satu produk', 'error');
        return;
    }
    
    if (!$('#customer_id').val()) {
        showToast('Pilih pelanggan', 'error');
        return;
    }
    
    showToast('Fitur rental akan segera diimplementasikan', 'info');
}

// Save draft
function saveDraft() {
    showToast('Fitur simpan draft akan segera tersedia', 'info');
}

// Format number
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
</script>
